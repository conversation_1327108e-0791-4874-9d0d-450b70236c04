GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-import (2.2.0)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    acts_as_list (1.2.4)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ancestry (4.3.3)
      activerecord (>= 5.2.6)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    ast (2.4.3)
    aws-eventstream (1.4.0)
    aws-partitions (1.1132.0)
    aws-sdk-core (3.227.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.107.0)
      aws-sdk-core (~> 3, >= 3.227.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.193.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.1.0)
      racc
    browser (5.3.1)
    builder (3.3.0)
    bullet (8.0.8)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (12.0.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    cgi (0.5.0)
    childprocess (5.1.0)
      logger (~> 1.5)
    chunky_png (1.4.0)
    cocoon (1.2.15)
    coderay (1.1.3)
    combine_pdf (1.0.29)
      matrix
      ruby-rc4 (>= 0.1.5)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    countries (7.1.1)
      unaccent (~> 0.3)
    crass (1.0.6)
    cssbundling-rails (1.4.3)
      railties (>= 6.0.0)
    csv (3.3.5)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise_token_auth (1.2.5)
      bcrypt (~> 3.0)
      devise (> 3.5.2, < 5)
      rails (>= 4.2.0, < 8.1)
    dotenv (3.1.8)
    dotenv-rails (3.1.8)
      dotenv (= 3.1.8)
      railties (>= 6.1)
    drb (2.2.3)
    email_reply_parser (0.5.11)
    erb (4.0.4)
      cgi (>= 0.3.3)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    faker (3.5.2)
      i18n (>= 1.8.11, < 2)
    faraday (2.13.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    faraday-retry (2.3.2)
      faraday (~> 2.0)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-darwin)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    gapic-common (1.0.1)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      google-protobuf (>= 3.25, < 5.a)
      googleapis-common-protos (~> 1.6)
      googleapis-common-protos-types (~> 1.15)
      googleauth (~> 1.12)
      grpc (~> 1.66)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-cloud-ai_platform (2.0.1)
      google-cloud-ai_platform-v1 (~> 1.0)
      google-cloud-core (~> 1.6)
    google-cloud-ai_platform-v1 (1.18.0)
      gapic-common (~> 1.0)
      google-cloud-errors (~> 1.0)
      google-cloud-location (~> 1.0)
      google-iam-v1 (~> 1.3)
    google-cloud-core (1.8.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.3.1)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.5.0)
    google-cloud-location (1.1.0)
      gapic-common (~> 1.0)
      google-cloud-errors (~> 1.0)
    google-iam-v1 (1.4.0)
      gapic-common (~> 1.0)
      google-cloud-errors (~> 1.0)
      grpc-google-iam-v1 (~> 1.11)
    google-logging-utils (0.2.0)
    google-protobuf (4.31.1-arm64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-x86_64-darwin)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos (1.7.0)
      google-protobuf (>= 3.18, < 5.a)
      googleapis-common-protos-types (~> 1.7)
      grpc (~> 1.41)
    googleapis-common-protos-types (1.20.0)
      google-protobuf (>= 3.18, < 5.a)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    groupdate (6.5.1)
      activesupport (>= 7)
    grpc (1.73.0-arm64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.73.0-x86_64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc-google-iam-v1 (1.11.0)
      google-protobuf (>= 3.18, < 5.a)
      googleapis-common-protos (~> 1.7.0)
      grpc (~> 1.41)
    hashie (5.0.0)
    hiredis-client (0.25.1)
      redis-client (= 0.25.1)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.9.0)
      mutex_m
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    icalendar (2.11.2)
      base64
      ice_cube (~> 0.16)
      logger
      ostruct
    ice_cube (0.17.0)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    jsbundling-rails (1.3.1)
      railties (>= 6.0.0)
    json (2.13.0)
    jwt (2.10.2)
      base64
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    letter_opener_web (3.0.0)
      actionmailer (>= 6.1)
      letter_opener (~> 1.9)
      railties (>= 6.1)
      rexml
    levenshtein (0.2.2)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    mailgun-ruby (1.3.7)
      faraday (~> 2.1)
      faraday-multipart (~> 1.1.0)
      mime-types
    marcel (1.0.4)
    matrix (0.4.3)
    method_source (1.1.0)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0715)
    mini_mime (1.1.5)
    minitest (5.25.5)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    msgpack (1.8.0)
    multi_json (1.17.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mutex_m (0.3.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.3)
    parallel (1.27.0)
    paranoia (3.0.1)
      activerecord (>= 6, < 8.1)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pdf-core (0.10.0)
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    prettyprint (0.2.0)
    prism (1.4.0)
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    pusher (2.0.3)
      httpclient (~> 2.8)
      multi_json (~> 1.15)
      pusher-signature (~> 0.1.8)
    pusher-signature (0.1.8)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-cors (3.0.0)
      logger
      rack (>= 3.0.14)
    rack-mini-profiler (4.0.0)
      rack (>= 1.2.0)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    redis-client (0.25.1)
      connection_pool
    regexp_parser (2.10.0)
    reline (0.6.2)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.4.1)
    rqrcode (2.2.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 1.0)
    rqrcode_core (1.2.0)
    rubocop (1.35.1)
      json (~> 2.3)
      parallel (~> 1.10)
      parser (>= 3.1.2.1)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.20.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 3.0)
    rubocop-ast (1.46.0)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-minitest (0.27.0)
      rubocop (>= 0.90, < 2.0)
    rubocop-performance (1.19.1)
      rubocop (>= 1.7.0, < 2.0)
      rubocop-ast (>= 0.4.0)
    rubocop-rails (2.25.1)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails-omakase (1.0.0)
      rubocop
      rubocop-minitest
      rubocop-performance
      rubocop-rails
    rubocop-rspec (2.17.1)
      rubocop (~> 1.33)
    ruby-progressbar (1.13.0)
    ruby-rc4 (0.1.5)
    rubyzip (2.4.1)
    rufus-scheduler (3.9.2)
      fugit (~> 1.1, >= 1.11.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    securerandom (0.4.1)
    selenium-webdriver (4.32.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sentry-rails (5.26.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.26.0)
    sentry-ruby (5.26.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-scheduler (6.0.1)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 7.3, < 9)
    sidekiq-unique-jobs (8.0.11)
      concurrent-ruby (~> 1.0, >= 1.0.5)
      sidekiq (>= 7.0.0, < 9.0.0)
      thor (>= 1.0, < 3.0)
    signet (0.20.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    stripe (15.3.0)
    thor (1.4.0)
    tilt (2.6.1)
    timeout (0.4.3)
    truncate_html (0.9.3)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unaccent (0.4.0)
    unicode-display_width (2.6.0)
    uniform_notifier (1.17.0)
    uri (1.0.3)
    useragent (0.16.11)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wicked (2.0.0)
      railties (>= 3.0.7)
    wicked_pdf (2.8.2)
      activesupport
      ostruct
    will_paginate (4.0.1)
    wkhtmltopdf-binary (********)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.18)

GEM
  remote: https://dl.fontawesome.com/basic/fontawesome-pro/ruby/
  specs:
    font-awesome-pro-sass (6.6.0)
      sassc (~> 2.0)

PLATFORMS
  arm64-darwin
  x86_64-darwin

DEPENDENCIES
  activerecord-import
  acts_as_list
  ancestry
  annotate
  aws-sdk-s3 (~> 1.114)
  benchmark
  bootsnap
  brakeman
  browser
  bullet
  byebug
  capybara
  cocoon
  combine_pdf (= 1.0.29)
  countries
  cssbundling-rails
  debug
  devise
  devise_token_auth
  dotenv-rails
  email_reply_parser
  faker
  ffi
  font-awesome-pro-sass (= 6.6.0)!
  google-cloud-ai_platform
  groupdate
  hiredis-client
  httparty
  icalendar
  importmap-rails
  jbuilder
  jsbundling-rails
  letter_opener
  letter_opener_web
  levenshtein (~> 0.2.2)
  mailgun-ruby
  money
  nokogiri (~> 1.18.0)
  omniauth
  parallel
  paranoia
  pg (~> 1.1)
  prawn
  pry
  puma (>= 5.0)
  pundit
  pusher
  rack-cors
  rack-mini-profiler
  rails (~> 7.2.2, >= *******)
  ransack
  rqrcode (= 2.2.0)
  rubocop (~> 1.35.0)
  rubocop-rails (~> 2.8)
  rubocop-rails-omakase
  rubocop-rspec (~> 2.12)
  rubyzip
  sassc-rails
  selenium-webdriver
  sentry-rails
  sentry-ruby
  sidekiq
  sidekiq-scheduler
  sidekiq-unique-jobs
  sprockets-rails
  stimulus-rails
  stripe
  truncate_html
  turbo-rails
  tzinfo-data
  web-console
  wicked
  wicked_pdf
  will_paginate
  wkhtmltopdf-binary (= ********)

BUNDLED WITH
   2.6.7
