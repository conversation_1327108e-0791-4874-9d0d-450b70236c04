# frozen_string_literal: true

module Patients
  class CalendarService
    def initialize(params, current_patient)
      @params = params
      @current_patient = current_patient
    end

    def call
      @patient = current_patient
      fetch_charted_treatment_values
      fetch_new_appointment_values
      @dentists = User.clinicians.without_archived
      @current_dentist = @dentists.where(id: params[:dentist_id] || @charted_treatment&.practitioner_id).first
      fetch_slots
      result
    end

    private

    attr_reader :params, :current_patient

    def fetch_charted_treatment_values
      return if params[:charted_treatment_id].blank?

      @charted_treatment = ChartedTreatment.find_by(id: params[:charted_treatment_id])
      @practice = @charted_treatment&.treatment&.practice
      @duration = @charted_treatment&.duration || @charted_treatment&.treatment&.duration
      @treatment = @charted_treatment&.treatment
    end

    def fetch_new_appointment_values
      return if params[:calendar_reserved_block_type_id].blank?

      @reserved_block = CalendarReservedBlockType.find_by(id: params[:calendar_reserved_block_type_id])
      @treatment = @reserved_block&.treatment
      @duration = @treatment.duration
      @practice = @treatment.practice
      @dentists = User.includes(:image_attachment).joins(:practices).clinicians.without_archived
                      .where(id: @reserved_block.user_ids, practices: { id: @practice.id })
    end

    def fetch_slots
      @duration ||= 60
      service_params = {
        practitioners: @current_dentist,
        start_date: (params[:date].present? ? Date.parse(params[:date]) : Date.current).beginning_of_month.to_s,
        end_date: (params[:date].present? ? Date.parse(params[:date]) : Date.current).end_of_month.to_s,
        start_time: params[:start_time].presence || '00:00',
        end_time: params[:end_time].presence || '23:59',
        duration: @duration,
        practice: @practice,
        reserved_block: @reserved_block
      }
      @slots = CalendarSlotFinder.new(service_params).run
    end

    def result
      [@patient, @dentists, @current_dentist, @slots, @practice, @duration, @treatment, @charted_treatment, @reserved_block]
    end
  end
end
