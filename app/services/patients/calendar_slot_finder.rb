# frozen_string_literal: true

module Patients
  class CalendarSlotFinder
    def initialize(params)
      @params = params
      @slot_finder_service = ::Calendar::SlotFinder.new(
        practitioners: params[:practitioner],
        start_date: params[:start_date],
        end_date: params[:end_date],
        start_time: params[:start_time],
        end_time: params[:end_time],
        duration: params[:duration],
        practice: params[:practice]
      )
    end

    def run
      @service_result = slot_finder_service.run
      filter_by_reserved_block
      result
    end

    private

    attr_reader :params, :slot_finder_service, :service_result

    def result
      service_result.group_by { |slot| slot.begin.strftime('%Y-%m-%d') }
    end

    def filter_by_reserved_block
      if params[:reserved_block].blank? ||
         params[:reserved_block]&.calendar_reserved_blocks.blank?

        @service_result = [] if params[:reserved_block]&.only_bookable_within_reserved_slots
        return
      end

      @service_result = @service_result.select do |slot|
        slots_reserved_block = params[:reserved_block].calendar_reserved_blocks.select do |block|
          block.practitioner_id == params[:practitioner].id && block.start_time.to_date == slot.begin.to_date
        end.first

        next false if slots_reserved_block.blank?

        correct_start_time = slots_reserved_block.start_time.seconds_since_midnight <= slot.begin.seconds_since_midnight
        correct_end_time = slots_reserved_block.end_time.seconds_since_midnight >= slot.end.seconds_since_midnight
        correct_start_time && correct_end_time
      end
    end
  end
end
