# frozen_string_literal: true

class LabWorkStatusUpdater
  def initialize(lab_work, status:, dentist_id: nil, current_user: nil)
    @lab_work     = lab_work
    @status       = status
    @dentist_id   = dentist_id
    @current_user = current_user
  end

  def call
    @lab_work.current_user = @current_user if @lab_work.respond_to?(:current_user=)

    update_attributes = { status: @status }

    case @status
    when 'Arrived At Practice - Dentist Check Required'
      update_attributes.merge!(
        checked_in_by: User.find_by(id: @dentist_id) || @current_user,
        arrived_at: Time.current
      )
    when 'Checked By Dentist - Ready For Patient'
      update_attributes.merge!(
        quality_checked_by: User.find_by(id: @dentist_id) || @current_user
      )
    end

    if @lab_work.update(update_attributes)
      if @lab_work.previous_changes.key?('status')
        log_status_change
        notify_status_changed
      end
      true
    else
      false
    end
  end

  private

  def log_status_change
    status_to_title = {
      'Sent To Lab' => 'Sent to Lab',
      'Draft' => 'Status changed to Draft',
      'Arrived At Practice - Dentist Check Required' => 'Arrived at Practice',
      'Checked By Dentist - Ready For Patient' => 'Quality checked',
      'Fitted Successfully - Auto Archive' => 'Fitted and Archived',
      'Adjustment Required (Amend or Create New Lab Docket)' => 'Adjustment Required',
      'Lab Error - Adjustment or Remake Required' => 'Lab Error - Changes Required',
      'Archive Case - Not proceeding' => 'Archived',
      'Late' => 'Status changed to Late'
    }

    title = status_to_title[@lab_work.status]
    return if title.blank?

    LabWorkLog.create(
      title: title,
      created_by_id: @current_user&.id,
      lab_work_id: @lab_work.id
    )
  end

  def notify_status_changed
    recipients = @lab_work.all_related_users

    ld = @lab_work.lab_dockets&.last
    docket_items = ld&.lab_docket_items
    name = docket_items&.first&.lab_items&.first&.name || 'Lab Work'

    text = <<~TEXT.strip
      The status of lab work for <strong>#{name}</strong> for #{@lab_work.patient.full_name} has been updated to <strong>#{@lab_work.status}</strong>. Please review the case and take any necessary action based on the updated status.
    TEXT

    title = <<~TEXT.strip
      #{name} #{docket_items.size > 1 ? 'and other lab-work ' : ''}for #{@lab_work.patient.full_name} has been updated to #{@lab_work.status}
    TEXT

    recipients.each do |recipient|
      Notification.create(
        recipient: recipient,
        title: title,
        description: text,
        data: { type: 'labwork', lab_name: @lab_work.lab.name, color: '#EA580C' },
        actions: [
          { text: 'View Lab Work', primary: true, action: 'redirect', href: "/admin/lab_works/#{@lab_work.id}" },
          { text: 'Mark Read', action: 'mark_as_read' },
          { text: 'Remind Me Later', action: 'remind_in' }
        ]
      )
    end
  end
end
