# frozen_string_literal: true

module Calendar
  class NotificationCreator
    attr_reader :booking, :appointment, :appointment_type, :total_duration, :duration, :recipients

    def initialize(booking, charting_appointment)
      @booking = booking
      @appointment = charting_appointment
      @total_duration = @appointment&.charted_treatments&.sum(:duration).to_i
      @duration = booking.duration_in_minutes
      @appointment_type = booking.treatment&.patient_friendly_name || 'Other'
      @recipients = booking.all_related_users
    end

    def check_booking_duration
      return if appointment.nil? || total_duration.zero?

      if total_duration < duration
        overbooked
      elsif total_duration > duration
        underbooked
      end
    end

    private

    def overbooked
      difference = duration - total_duration
      title = "Appointment for #{booking.patient.full_name} Has Been Overbooked by #{booking.booked_by.full_name}"
      description = <<~TEXT.strip
        <strong>#{appointment_type}</strong> booked for #{booking.patient.full_name} is <strong>#{difference} minutes longer</strong> than required. It has been scheduled for <strong>#{duration} minutes</strong>, whereas only <strong>#{total_duration} minutes</strong> is required as per the charting notes - an overbooking of <strong>#{difference} minutes</strong>.
      TEXT
      send_notifications(title, description)
    end

    def underbooked
      difference = total_duration - duration
      title = "Appointment for #{booking.patient.full_name} Has Been Under-booked by #{booking.booked_by.full_name}"
      description = <<~TEXT.strip
        <strong>#{appointment_type}</strong> booked for #{booking.patient.full_name} has been scheduled with <strong>insufficient time</strong>. It has been booked for <strong>#{duration} minutes</strong>, whereas <strong>#{total_duration} minutes</strong> is required as per the charting notes — a shortfall of <strong>#{difference} minutes</strong>.
      TEXT
      send_notifications(title, description)
    end

    def send_notifications(title, description)
      recipients.each do |recipient|
        Notification.create(
          recipient: recipient,
          title: title,
          description: description,
          data: {
            type: 'appointment_update',
            sender: booking.patient.full_name
          },
          actions: [
            { text: 'View Calendar', primary: true, action: 'redirect', href: '/admin/calendar_bookings/staff_calendar' },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end
    end
  end
end
