# frozen_string_literal: true

module Calendar
  class SlotFinder
    AVAILABLE_START_TIME_MINS = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55].freeze
    STEP = 5.minutes

    # TODO: use practice hours
    SHIFT_START_TIME = '08:00'
    SHIFT_END_TIME = '22:00'

    attr_reader :practitioners, :start_date, :end_date, :start_time, :end_time, :duration, :practice

    def initialize(practitioners:, start_date:, end_date:, start_time:, end_time:, duration:, practice:)
      @practitioners = Array(practitioners)
      @start_date = Date.parse(start_date)
      @end_date = Date.parse(end_date)
      @start_time = [SHIFT_START_TIME, start_time].max
      @end_time = [SHIFT_END_TIME, end_time].min
      @duration = duration.to_i
      @practice = practice
    end

    def run
      available_slots = []

      practitioners.each do |practitioner|
        reserved_blocks = CalendarReservedBlock.where(practitioner: practitioner).between_dates(start_date, end_date)

        shifts_by_date = Shift
                         .where(user_id: practitioner.id)
                         .where(practice_id: practice.id)
                         .where('event_start < ? AND event_end > ?', end_date.end_of_day, start_date.beginning_of_day)
                         .clinical
                         .select(:event_start, :event_end)
                         .order(:event_start)
                         .group_by { |sch| sch.event_start.to_date }

        (start_date..end_date).each do |date|
          day_shifts = shifts_by_date[date] || []

          next if day_shifts.empty?

          day_shifts.each do |shift|
            shift_start = shift.event_start
            shift_end   = shift.event_end

            day_start = [Time.parse("#{date} #{start_time} London"), shift_start].max
            day_end   = [Time.parse("#{date} #{end_time}   London"), shift_end].min

            next if day_start >= day_end

            current_time = round_down_to_5_minutes(day_start)

            while current_time + duration.minutes <= day_end
              if AVAILABLE_START_TIME_MINS.include?(current_time.min) &&
                 !overlaps_with_existing_booking?(current_time, current_time + duration.minutes, practitioner)

                slot_end = current_time + duration.minutes

                treatment_name = reserved_slot_treatment_name(current_time, slot_end, practitioner, reserved_blocks)

                available_slots << CalendarSlot.new(current_time, slot_end, practitioner,
                                                    preferred: preferred_slot?(current_time, slot_end, practitioner),
                                                    reserved: treatment_name.present?,
                                                    reserved_treatment_name: treatment_name)
              end

              current_time += STEP
            end
          end
        end
      end

      available_slots.sort_by(&:begin)
    end

    private

    def bookings_to_check(practitioner)
      # TODO: exclude current booking if rescheduling
      @bookings_to_check ||= {}
      @bookings_to_check[practitioner.id] ||= practitioner
                                              .practitioner_bookings
                                              .where(practice_id: practice.id)
                                              .not_cancelled
                                              .between_dates(start_date, end_date)
                                              .to_a
    end

    def overlaps_with_existing_booking?(slot_start, slot_end, practitioner)
      bookings_to_check(practitioner).any? do |booking|
        booking.overlaps?(slot_start, slot_end)
      end
    end

    def preferred_slot?(slot_start, slot_end, practitioner)
      # NOTE: slot is preferred if slot_start or slot_end is glued to some existing booking
      bookings_to_check(practitioner).any? do |booking|
        slot_start == booking.end_time || slot_end == booking.start_time
      end
    end

    def reserved_slot_treatment_name(slot_start, slot_end, practitioner, reserved_blocks)
      return false unless practitioner

      reserved_block = reserved_blocks.find do |block|
        block_start_seconds = block.start_time.seconds_since_midnight
        block_end_seconds = block.end_time.seconds_since_midnight
        slot_start_seconds = slot_start.seconds_since_midnight
        slot_end_seconds = slot_end.seconds_since_midnight

        slot_start_seconds >= block_start_seconds && slot_end_seconds <= block_end_seconds &&
          block.start_time.to_date == slot_start.to_date
      end

      reserved_block.present? ? reserved_block.calendar_reserved_block_type.treatment.patient_friendly_name : nil
    end

    def round_down_to_5_minutes(time)
      rounded_minutes = (time.min / 5) * 5
      Time.new(time.year, time.month, time.day, time.hour, rounded_minutes, 0, time.utc_offset).in_time_zone
    end
  end
end
