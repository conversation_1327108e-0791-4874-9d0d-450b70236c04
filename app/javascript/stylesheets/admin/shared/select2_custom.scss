$input-height-base: 34px;

.select2-container--default {
  display: block;
  .select2-selection--single, .select2-selection--multiple {
    border: 1px solid $border-color;
    border-radius: 8px;
    .select2-selection__clear{
      font-weight: 300;
      font-size: 20px;
      margin-top: 0px;
      color: #444;
      &:hover{
        color: #DC3545;
      }
    }
  }
  .select2-selection--multiple{
    display: flex;
    flex-wrap: wrap;

    .select2-selection__clear{
      margin-top: 3px;
      margin-right: 0px;
      right: 6px;
      position: absolute;
    }

    .select2-selection__rendered {
      margin: 0;
      margin-left: -12px;

      &:empty {
        display: none;
      }
    }

    .select2-selection__choice {
      border-color: #F7B07C !important;
      background-color: rgba(247, 176, 124, 0.4) !important;

      .select2-selection__choice__display {
        padding-left: 16px;
        padding-right: 0px;
        color: black !important;
      }

      .select2-selection__choice__remove {
        color: black !important;
        border-right-color: #F7B07C !important;
      }
    }
  }

  .select2-selection--single {
    height: 34px;

    .select2-selection__rendered{
      padding: 6px 24px 6px 12px;
      height: 34px;
      line-height: 20px;
      font-size: $font-size-base + 1px;
      color: $black-color;
    }

    .select2-selection__arrow {
      height: 32px;
      width: 24px;

      b {
        border: 0;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        margin: 0;

        &:after {
          content: "\f078";
          font-size: 10px;
          font-weight: normal;
          line-height: 32px;
          font-family: 'Font Awesome 6 Pro';
        }
      }
    }
  }

  .select2-selection--multiple {
    min-height: 34px;
    .select2-selection__rendered {
      padding: 0px 12px;
    }

    .select2-selection__choice {
      border-radius: 15px;
      background: $light-color;
      color: $body-color;
      border: 1px solid darken($light-gray-color, 20%);
      padding: 0px 12px 0px 8px;
    }

    .select2-selection__choice__remove {
      color: $body-color;
      margin-right: 6px;

      &:hover {
        color: $danger-color;
      }
    }

    .select2-search--inline{
      .select2-search__field{
        margin-top: 6px;
        width: 100%!important;
      }
    }
  }

  &.select2-container--default.select2-container--focus {
    .select2-selection--multiple {
      border: 1px solid $border-color;
    }
  }

  &.select2-container--open {
    .select2-selection--single {
      .select2-selection__arrow {
        b {
          &:after {
            content: "\f077";
          }
        }
      }
    }

    .select2-dropdown--below {
      top: -1px;
    }
  }

  .select2-results__group {
    font-size: 12px;
  }

  .select2-results__option {
    padding: 6px 12px;
  }

  .select2-results__option[aria-selected="true"] {
    background-color: darken($light-color, 3%)
  }

  .select2-results__option--highlighted[aria-selected] {
    background-color: $primary-color;
  }

  .select2-dropdown {
    border-radius: 0;
    border-width: 1px;
    border-color: $border-color;
  }

  .select2-search--dropdown {
    background-color: lighten($light-gray-color, 5%);
    border-bottom: 1px solid $light-gray-color;

    .select2-search__field {
      background-color: transparent;
      border-width: 0;
      outline: none;
    }
  }
}

.select2-search__field {
  min-height: 24px;
  font-size: 14px !important;
  font-family: Poppins, sans-serif !important;
  margin: 0 !important;
}

.select2-selection__rendered {
  color: #444 !important;
}
