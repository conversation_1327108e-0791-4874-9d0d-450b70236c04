#announcements-container {
  .cancelbtn {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    margin-right: 6px;
    padding: 8px;
    border-radius: 37px;
    border: 1px solid #2A4558;
    color: #2A4558;
    background: white;
    justify-content: center;
    transition: background-color 0.3s ease;
    cursor: pointer;
    height: 37px;
    font-size: 14px;
    width: 120px;
  }

  .card {
    background: white;
  }

  .title {
    font-size: 26px;
    font-weight: bold;
    color: #151515;
  }

  .header {
    padding: 10px 20px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
  }

  #userAnnouncementSearch {
    width: 250px;
    font-size: 14px;
    padding: 9px 6px 9px 30px;
    padding-left: 34px !important;
    border-radius: 20px;
    background: #FFF;
    box-shadow: 0px 3px 14px 0px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
  }

  #userAnnouncementSearch:focus {
    box-shadow: none;
    border-color: #2A4558;
  }

  .select2-search__field{
    padding-left: 6px !important;
    font-family: Poppins;
  }

  .form-control, .form-select, .form-label, .form-check-label, .form-check-input, .form-check {
    font-size: 14px;
    font-weight: 400;
    font-family: 'Poppins';
  }

  .form-check {
    margin-left: 1rem;
  }

  .form-check .form-check-input {
    margin-left: -2rem;
  }

  .offcanvas-header {
    padding: 19px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    border-bottom: 1px solid #e5e5e5;
    margin: 0 4px 0 20px;
  }

  .offcanvas-title {
    color: #151515;
    font-family: "Poppins", sans-serif;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .offcanvas, .offcanvas-body {
    width: 600px !important;
    z-index: 1051;
  }

  .fixed-footer {
    z-index: 1052;
  }

  .fixed-footer.show {
    transform: translateY(0);
    display: flex !important;
  }

  #show-selected-images img {
    width: 100px;
    height: 100px;
    margin: 5px;
  }

  .emoji {
    font-size: 25px;
    padding: 5px;
  }

  .card {
    border-radius: 20px;
    --bs-border-radius: 20px;
    background: white;
    padding: 30px;
    font-family: "Poppins", sans-serif;
    color: black;
    border: 0;
    text-decoration: none;
  }

  .announcements {
    padding: 0;

    .reaction {
      font-size: 25px;
      margin-right: 20px;

      small {
        color: #2A4558;
        font-size: 17.6px;
      }

      cursor: pointer;
    }

    .reaction-button {
      margin-left: 12px;
    }

    .reactions {
      color: #2A4558;
      font-size: 12px;
      line-height: 120%;
    }

    .shared {
      color: #2A4558;
      text-align: center;
      font-size: 12px;
      line-height: 120%;
    }

    .dropdown-btn {
      height: 34px;
      width: 34px;
      padding: 0.25rem;
      border-radius: 50%;
      border: 1px solid #2a4558;
      background-color: #ffffff00;
    }

    .dropdown-btn:hover {
      background-color: #6c757d;
      border-color: #6c757d;
    }

    .card-header {
      border-bottom: 1px solid white;
      background-color: #D1E0B7;
      padding: 0.75rem 1rem;
    }

    .card-footer {
      background-color: white;
      border-top: 1px solid #D8D8D8;
      padding: 0.75rem 1rem;
    }

    .name {
      color: #2A4558;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 120%;
      margin-bottom: 8px;
    }

    .date {
      color: #8999B0;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 120%;
    }

    .badge {
      text-decoration: none;
      background-color: #B97172;
      color: white;
      border: 1px solid #2A4558;
      padding: 0.25rem 0.5rem;
      border-radius: 1rem;
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      line-height: 120%;
    }
  }

  .search-bar{
    height: 34.8px;
    width: 240px;
    border-radius: 20px;
    background: #FFF;
    box-shadow: 0 3px 14px 0 rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
  }
}

.select2-container {
  z-index: 1060;
}

.select2-selection__choice, .select2-selection__choice__remove{
  color: #212529 !important;
}

.select2-selection__choice__display{
  padding-left: 15px !important;
}
