document.addEventListener("DOMContentLoaded", () => {
  if (!$('#customer-medical-form').length) return;

  const informationModal = document.getElementById("information-modal");
  informationModal.classList.remove("hidden");

  const continueButton = document.querySelector('#information-modal .information-modal-continue');
  continueButton.addEventListener('click', () => {
    informationModal.classList.add('hidden');
  });

  const pages = document.querySelectorAll(".question-page");
  const progressText = document.querySelector(".current-step");
  const progressBar = document.querySelector(".half-width-bar");
  const nextBtn = document.querySelector(".next-btn");
  const prevBtn = document.querySelector(".prev-btn");
  const submitBtn = document.querySelector(".review-btn");

  let currentPage = 0;

  function showPage(index) {
    pages.forEach((page, i) => {
      page.classList.toggle("hidden", i !== index);
    });
    currentPage = index;

    // Progress bar
    const total = pages.length;
    const step = currentPage + 1;
    if (progressBar) progressBar.style.width = `${(step / total) * 100}%`;
    if (progressText) progressText.textContent = step;

    // Show/hide buttons
    prevBtn.style.display = currentPage === 0 ? "none" : "inline-block";

    if (currentPage === pages.length - 1) {
      nextBtn.style.display = "none";
      submitBtn?.classList.remove("hidden");
    } else {
      nextBtn.style.display = "inline-block";
      submitBtn?.classList.add("hidden");
    }

    window.scrollTo({ top: 0, behavior: "smooth" });
  }

  // Initial display
  showPage(0);

  nextBtn?.addEventListener("click", () => {
    if (currentPage < pages.length - 1) showPage(currentPage + 1);
  });

  prevBtn?.addEventListener("click", () => {
    if (currentPage > 0) showPage(currentPage - 1);
  });

  // Optional: toggle follow-up questions for multiple or single select
  document.querySelectorAll(".option-select").forEach((input) => {
    input.addEventListener("change", (e) => {
      const answerId = e.target.dataset.answerId;
      const questionId = e.target.dataset.questionId;

      if (e.target.type === "radio") {
        document.querySelectorAll(`[id^="followup-"][id$="-${questionId}"]`).forEach(div => {
          div.classList.add("hidden");
        });
        const target = document.getElementById(`followup-${answerId}-${questionId}`);
        if (target) target.classList.remove("hidden");
      } else {
        const target = document.getElementById(`followup-${answerId}-${questionId}`);
        if (target) target.classList.toggle("hidden", !e.target.checked);
      }
    });
  });

  $('.mh-s2').select2({
    placeholder: "Select Medication",
    allowClear: true,
    width: "100%"
  });

  document.querySelectorAll('.select2-container.select2-container--default').forEach(selector => {
    selector.style.border = '1px solid #E2E8F0';
    selector.style.borderRadius = '8px';
  })

  // const questions = document.querySelectorAll(".question-step");
  // let currentIndex = 0;
  //
  // function showQuestion(index) {
  //   questions.forEach((q, i) => {
  //     q.classList.toggle("hidden", i !== index);
  //   });
  //   currentIndex = index;
  //   updateProgressBar();
  // }
  //
  // function updateProgressBar() {
  //   const total = questions.length;
  //   const bar = document.querySelector(".half-width-bar");
  //   if(!bar) return;
  //   const step = currentIndex + 1;
  //   const widthPercent = (step / total) * 100;
  //   bar.style.width = widthPercent + "%";
  // }
  //
  // showQuestion(0);
  //
  // document.querySelectorAll(".next-btn").forEach((btn, i) => {
  //   btn.addEventListener("click", () => {
  //     if(currentIndex < questions.length - 1) {
  //       showQuestion(currentIndex + 1);
  //     }
  //   });
  // });
  //
  // document.querySelectorAll(".prev-btn").forEach((btn, i) => {
  //   btn.addEventListener("click", () => {
  //     if(currentIndex > 0) {
  //       showQuestion(currentIndex - 1);
  //     }
  //   });
  // });
  //
  // document.querySelectorAll(".option-select").forEach((input) => {
  //   input.addEventListener("change", (e) => {
  //     const answerId = e.target.dataset.answerId;
  //     const questionId = e.target.dataset.questionId;
  //
  //     if(e.target.type === "radio") {
  //       const allSubs = document.querySelectorAll(`[id^="followup-"][id$="-${questionId}"]`);
  //       allSubs.forEach(div => div.classList.add("hidden"));
  //       const relevant = document.getElementById(`followup-${answerId}-${questionId}`);
  //       if(relevant) relevant.classList.remove("hidden");
  //     } else {
  //       const followupDiv = document.getElementById(`followup-${answerId}-${questionId}`);
  //       if(followupDiv) {
  //         if(e.target.checked) {
  //           followupDiv.classList.remove("hidden");
  //         } else {
  //           followupDiv.classList.add("hidden");
  //         }
  //       }
  //     }
  //   });
  // });
});
