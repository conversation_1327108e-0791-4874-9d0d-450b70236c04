$(document).ready(function () {
    if (!$('.ld-treatments-container').length) return;

    var show = !!$('.ld-treatments-container').data('show');

    function switchTab(targetId) {
        $(".ldi").addClass("hidden");
        $(targetId).removeClass("hidden");

        $(".item-tab").removeClass("bg-white text-blue-700 shadow");
        $(`.item-tab[data-target='${targetId}']`).addClass("bg-white text-blue-700 shadow");
    }

    $(document).on('change', '.ldt-type', function () {
        const selectedType = $(this).val();
        const container = $(this).closest('div.ldt-selects-container');
        const $labSelect = container.find('.ldt-lab-item-select select');

        $labSelect.find('option').each(function () {
            const optionType = $(this).data('treatment-type');

            if (!selectedType || !optionType || optionType === selectedType) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });

        if ($labSelect.find('option:selected').is(':hidden')) {
            $labSelect.val('');
        }
    });

    function amendTab(item) {
        $(item).find("input, select, textarea").prop("disabled", true);
        $(item).find(".ldi-assocs, .remove_fields").remove();
    }

    function renderTreatment(container, option) {
        if (!option.value) return;

        const treatmentType = $(option).data("treatment-type"),
            optionName = $(option).text().trim(),
            baseName = $(option).parent().prop("name").replace("[lab_item_id]", ""),
            data = JSON.parse($(container).closest(".ldt").find(".ldt-data").text());

        let fields;

        switch (optionName.toLowerCase()) {
            case "crown":
                if (treatmentType == "NHS") {
                    fields = `<div class="lg:col-span-3">
                                <label class="block font-medium text-gray-700 mb-2" for="${baseName}[materials]">Materials</label>
                                <div class="select-wrapper relative mb-3 w-full">
                                    <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <option value="">Select</option>
                                        <option value="Porcelain Bonded">Porcelain Bonded</option>
                                        <option value="Full Metal">Full Metal</option>
                                        <option value="Composite">Composite</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="lg:col-span-3">
                                <label class="block font-medium text-gray-700 mb-2" for="${baseName}[post_core]">Post & Core</label>
                                <div class="select-wrapper relative mb-3 w-full">
                                    <select name="${baseName}[post_core]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <option value="">Select</option>
                                        <option value="Integral">Integral</option>
                                        <option value="Separate">Separate</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>`;
                } else {
                    fields = `<div class="lg:col-span-3">
                                <div class="flex flex-col">
                                    <label class="block font-medium text-gray-700 mb-2" for="${baseName}[materials]">Materials</label>
                                    <div class="select-wrapper relative mb-3 w-full">
                                        <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option value="">Select</option>
                                            <option value="Porcelain Bonded">Porcelain Bonded</option>
                                            <option value="Full Metal">Full Metal</option>
                                            <option value="Composite">Composite</option>
                                            <option value="Zirconia">Zirconia</option>
                                            <option value="Emax">Emax</option>
                                            <option value="Gold">Gold</option>
                                            <option value="Implant retained">Implant retained</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    
                                    <label class="block font-medium text-gray-700 mb-2" for="${baseName}[post_core_materials]">Post & Core Materials</label>
                                    <div class="select-wrapper relative mb-3 w-full">
                                        <select name="${baseName}[post_core_materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option value="">Select</option>
                                            <option value="Non precious (co-cr)">Non precious (co-cr)</option>
                                            <option value="Semi precious (pd)">Semi precious (pd)</option>
                                            <option value="Precious (au)">Precious (au)</option>
                                            <option value="Porcelain">Porcelain</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-3">
                                <div class="flex flex-col">
                                    <label class="block font-medium text-gray-700 mb-2" for="${baseName}[margin]">Margin</label>
                                    <div class="select-wrapper relative mb-3 w-full">
                                        <select name="${baseName}[margin]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option value="">Select</option>
                                            <option value="No metal margin">No metal margin</option>
                                            <option value="Metal margin lingual/palatal">Metal margin lingual/palatal</option>
                                            <option value="Metal all around">Metal all around</option>
                                            <option value="Metal backing">Metal backing</option>
                                            <option value="Metal backing and palatal cusp">Metal backing and palatal cusp</option>
                                            <option value="Porcelain facing only">Porcelain facing only</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    
                                    <label class="block font-medium text-gray-700 mb-2" for="${baseName}[post_core]">Post & Core</label>
                                    <div class="select-wrapper relative mb-3 w-full">
                                        <select name="${baseName}[post_core]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option value="">Select</option>
                                            <option value="Integral">Integral</option>
                                            <option value="Separate">Separate</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>`;
                }
                break;
            case "implant crown":
                if (treatmentType == "NHS") {
                    fields = `<div class="lg:col-span-6 space-y-4">
                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                    <label for="${baseName}[materials]" class="block font-medium text-gray-700 mb-2">Materials</label>
                                    <div class="select-wrapper relative mb-3">
                                      <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <option>Select</option>
                                      </select>
                                    </div>
                                    <label for="${baseName}[margin]" class="block font-medium text-gray-700 mb-2">Margin</label>
                                    <div class="select-wrapper relative mb-3">
                                      <select name="${baseName}[margin]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <option>Select</option>
                                      </select>
                                    </div>
                                  </div>

                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                                    <label for="${baseName}[implant_system]" class="block font-medium text-gray-700 mb-2">Implant system</label>
                                    <div class="select-wrapper relative mb-3">
                                      <select name="${baseName}[implant_system]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <option>Select</option>
                                      </select>
                                    </div>
                                    <label for="${baseName}[retention]" class="block font-medium text-gray-700 mb-2">Retention</label>
                                    <div class="select-wrapper relative mb-3">
                                      <select name="${baseName}[retention]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <option>Select</option>
                                      </select>
                                    </div>
                                    <label for="${baseName}[abutment_material]" class="block font-medium text-gray-700 mb-2">Abutment material</label>
                                    <div class="select-wrapper relative mb-3">
                                      <select name="${baseName}[abutment_material]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <option>Select</option>
                                      </select>
                                    </div>
                                  </div>
                                </div>
                                `;
                } else {
                    fields = `<div class="lg:col-span-6 space-y-4">
                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                    <div>
                                        <label for="${baseName}[materials]" class="block font-medium text-gray-700 mb-2">Materials</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Porcelain Bonded">Porcelain Bonded</option>
                                            <option value="Full Metal">Full Metal</option>
                                            <option value="Composite">Composite</option>
                                            <option value="Zirconia">Zirconia</option>
                                            <option value="Emax">Emax</option>
                                            <option value="Gold">Gold</option>
                                            <option value="Hybrid">Hybrid</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label for="${baseName}[margin]" class="block font-medium text-gray-700 mb-2">Margin</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[margin]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="No metal margin">No metal margin</option>
                                            <option value="Metal margin lingual/palatal">Metal margin lingual/palatal</option>
                                            <option value="Metal all around">Metal all around</option>
                                            <option value="Metal backing">Metal backing</option>
                                            <option value="Metal backing and palatal cusp">Metal backing and palatal cusp</option>
                                            <option value="Porcelain facing only">Porcelain facing only</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                  </div>

                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                                    <div>
                                      <label for="${baseName}[implant_system]" class="block font-medium text-gray-700 mb-2">Implant system</label>
                                      <input name="${baseName}[implant_system]" class="w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    </div>
                                    
                                    <div>
                                        <label for="${baseName}[retention]" class="block font-medium text-gray-700 mb-2">Retention</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[retention]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Screw retained">Screw retained</option>
                                            <option value="Cement retained">Cement retained</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label for="${baseName}[abutment_material]" class="block font-medium text-gray-700 mb-2">Abutment material</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[abutment_material]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Chrome Cobalt">Chrome Cobalt</option>
                                            <option value="Titanium">Titanium</option>
                                            <option value="Zirconia with T base">Zirconia with T base</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                  </div>
                                </div>`;
                }
                break;
            case "implant retained bridge":
                fields = `<div class="lg:col-span-6 space-y-4">
                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                      <div>
                                        <label for="${baseName}[materials]" class="block font-medium text-gray-700 mb-2">Materials</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Porcelain Bonded">Porcelain Bonded</option>
                                            <option value="Full Metal">Full Metal</option>
                                            <option value="Composite">Composite</option>
                                            <option value="Zirconia">Zirconia</option>
                                            <option value="Emax">Emax</option>
                                            <option value="Gold">Gold</option>
                                            <option value="Hybrid">Hybrid</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                      </div>
                                    
                                    <div>
                                        <label for="${baseName}[margin]" class="block font-medium text-gray-700 mb-2">Margin</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[margin]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="No metal margin">No metal margin</option>
                                            <option value="Metal margin lingual/palatal">Metal margin lingual/palatal</option>
                                            <option value="Metal all around">Metal all around</option>
                                            <option value="Metal backing">Metal backing</option>
                                            <option value="Metal backing and palatal cusp">Metal backing and palatal cusp</option>
                                            <option value="Porcelain facing only">Porcelain facing only</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                  </div>

                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                    <div>
                                        <label for="${baseName}[retention]" class="block font-medium text-gray-700 mb-2">Retention</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[retention]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Screw retained">Screw retained</option>
                                            <option value="Cement retained">Cement retained</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label for="${baseName}[abutment_material]" class="block font-medium text-gray-700 mb-2">Abutment material</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[abutment_material]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Chrome Cobalt">Chrome Cobalt</option>
                                            <option value="Titanium">Titanium</option>
                                            <option value="Zirconia with T base">Zirconia with T base</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                  </div>
                                </div>`;
                break;
            case "implant retained denture":
                fields = `<div class="lg:col-span-6 space-y-4">
                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                    <div>
                                        <label for="${baseName}[materials]" class="block font-medium text-gray-700 mb-2">Material</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Acrylic">Acrylic</option>
                                            <option value="Hybrid">Hybrid</option>
                                            <option value="Zirconia">Zirconia</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                    
                                    <div>
                                      <label for="${baseName}[implant_system]" class="block font-medium text-gray-700 mb-2">Implant system</label>
                                      <input name="${baseName}[implant_system]" class="w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    </div>
                                  </div>

                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                      <div>
                                        <label for="${baseName}[retention]" class="block font-medium text-gray-700 mb-2">Retention</label>  
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[retention]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Screw retained">Screw retained</option>
                                            <option value="Cement retained">Cement retained</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                      </div>             
                                    
                                    <div>
                                        <label for="${baseName}[abutment_material]" class="block font-medium text-gray-700 mb-2">Abutment material</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[abutment_material]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Chrome Cobalt">Chrome Cobalt</option>
                                            <option value="Titanium">Titanium</option>
                                            <option value="Zirconia with T base">Zirconia with T base</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                  </div>
                                </div>`;
                break;
            case "bridge":
                if (treatmentType == "NHS") {
                    fields = `<div class="lg:col-span-6">
                                <label class="block font-medium text-gray-700 mb-2" for="${baseName}[materials]">Materials</label>
                                <div class="select-wrapper relative mb-3 w-full">
                                    <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <option value="">Select</option>
                                        <option value="Porcelain Bonded">Porcelain Bonded</option>
                                        <option value="Full Metal">Full Metal</option>
                                        <option value="Composite">Composite</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>`;
                } else {
                    fields = `<div class="lg:col-span-6">
                                <label class="block font-medium text-gray-700 mb-2" for="${baseName}[materials]">Materials</label>
                                <div class="select-wrapper relative mb-3 w-full">
                                    <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <option value="">Select</option>
                                        <option value="Porcelain Bonded">Porcelain Bonded</option>
                                        <option value="Full Metal">Full Metal</option>
                                        <option value="Composite">Composite</option>
                                        <option value="Zirconia">Zirconia</option>
                                        <option value="Emax">Emax</option>
                                        <option value="Implant retained">Implant retained</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>`;
                }
                break;
            case "mayland bridge":
            case "maryland bridge":
                fields = `<div class="lg:col-span-3">
                            <label class="block font-medium text-gray-700 mb-2" for="${baseName}[num_wings]">Number of wings</label>
                            <div class="select-wrapper relative mb-3 w-full">
                                <select name="${baseName}[num_wings]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="lg:col-span-3">
                            <label class="block font-medium text-gray-700 mb-2" for="${baseName}[materials]">Wing Materials</label>
                            <div class="select-wrapper relative mb-3 w-full">
                                <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select</option>
                                    <option value="Metal">Metal</option>
                                    <option value="Ceramic">Ceramic</option>
                                    <option value="PMMA">PMMA</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>`;
                break;
            case "denture":
                if (treatmentType == "NHS") {
                    fields = `<div class="lg:col-span-6 space-y-4">
                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                    <div>
                                        <label for="${baseName}[materials]" class="block font-medium text-gray-700 mb-2">Materials</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Acrylic">Acrylic</option>
                                            <option value="Metal Frame">Metal Frame</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>                                 
                                    
                                    <div>
                                        <label for="${baseName}[stage]" class="block font-medium text-gray-700 mb-2">Stage</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[stage]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Special tray">Special tray</option>
                                            <option value="Bite reg">Bite reg</option>
                                            <option value="Try in">Try in</option>
                                            <option value="Fit">Fit</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                  </div>

                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                    <div>
                                        <label for="${baseName}[treatment_type]" class="block font-medium text-gray-700 mb-2">Type</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[treatment_type]" class="select-other treatment-type-select w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="New Denture">New Denture</option>
                                            <option value="Addition">Addition</option>
                                            <option value="Reline">Reline</option>
                                            <option value="Add clasp">Add clasp</option>
                                            <option value="Temporary">Temporary</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                  </div>
                                </div>`;
                } else {
                    fields = `<div class="lg:col-span-6 space-y-4">
                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                    <div>
                                        <label for="${baseName}[materials]" class="block font-medium text-gray-700 mb-2">Materials</label>   
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Acrylic">Acrylic</option>
                                            <option value="Metal Frame">Metal Frame</option>
                                            <option value="Chrome Cobalt">Chrome Cobalt</option>
                                            <option value="Flexi Denture">Flexi Denture</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label for="${baseName}[stage]" class="block font-medium text-gray-700 mb-2">Stage</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[stage]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="Special tray">Special tray</option>
                                            <option value="Bite reg">Bite reg</option>
                                            <option value="Try in">Try in</option>
                                            <option value="Fit">Fit</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                  </div>

                                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                    <div>
                                        <label for="${baseName}[treatment_type]" class="block font-medium text-gray-700 mb-2">Type</label>
                                        <div class="select-wrapper relative mb-3">
                                          <select name="${baseName}[treatment_type]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                            <option>Select</option>
                                            <option value="New Denture">New Denture</option>
                                            <option value="Addition">Addition</option>
                                            <option value="Reline">Reline</option>
                                            <option value="Add clasp">Add clasp</option>
                                            <option value="Temporary">Temporary</option>
                                            <option value="Other">Other</option>
                                          </select>
                                        </div>
                                    </div>
                                  </div>
                                </div>`;
                }
                break;
            case "inlay/onlay/veneer":
                if (treatmentType == "NHS") {
                    fields = `<div class="lg:col-span-3">
                            <label class="block font-medium text-gray-700 mb-2" for="${baseName}[treatment_type]">Type</label>
                            <div class="select-wrapper relative mb-3 w-full">
                                <select name="${baseName}[treatment_type]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select</option>
                                    <option value="Inlay">Inlay</option>
                                    <option value="Onlay">Onlay</option>
                                    <option value="Veneer">Veneer</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="lg:col-span-3">
                            <label class="block font-medium text-gray-700 mb-2" for="${baseName}[materials]">Materials</label>
                            <div class="select-wrapper relative mb-3 w-full">
                                <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select</option>
                                    <option value="Composite">Composite</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>`;
                } else {
                    fields = `<div class="lg:col-span-3">
                            <label class="block font-medium text-gray-700 mb-2" for="${baseName}[treatment_type]">Type</label>
                            <div class="select-wrapper relative mb-3 w-full">
                                <select name="${baseName}[treatment_type]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select</option>
                                    <option value="Inlay">Inlay</option>
                                    <option value="Onlay">Onlay</option>
                                    <option value="Veneer">Veneer</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="lg:col-span-3">
                            <label class="block font-medium text-gray-700 mb-2" for="${baseName}[materials]">Materials</label>
                            <div class="select-wrapper relative mb-3 w-full">
                                <select name="${baseName}[materials]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select</option>
                                    <option value="Emax">Emax</option>
                                    <option value="Composite">Composite</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>`;
                }
                break;
            default:
                if (treatmentType == "NHS") {
                    fields = `<div class="lg:col-span-3">
                            <label class="block font-medium text-gray-700 mb-2" for="${baseName}[treatment_type]">Type</label>
                            <div class="select-wrapper relative mb-3 w-full">
                                <select name="${baseName}[treatment_type]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select</option>
                                    <option value="Diagnostic Wax up">Diagnostic Wax up</option>
                                    <option value="Study Model">Study Model</option>
                                    <option value="Mouth Guard">Mouth Guard</option>
                                    <option value="Night Guard">Night Guard</option>
                                    <option value="Essix Retainer">Essix Retainer</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="lg:col-span-3">
                            <label class="block font-medium text-gray-700 mb-2" for="${baseName}[arch]">Arch</label>
                            <div class="select-wrapper relative mb-3 w-full">
                                <select name="${baseName}[arch]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select</option>
                                    <option value="Top">Top</option>
                                    <option value="Bottom">Bottom</option>
                                    <option value="Both">Both</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>`;
                } else {
                    fields = `<div class="lg:col-span-3">
                            <label class="block font-medium text-gray-700 mb-2" for="${baseName}[treatment_type]">Type</label>
                            <div class="select-wrapper relative mb-3 w-full">
                                <select name="${baseName}[treatment_type]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select</option>
                                    <option value="Diagnostic Wax up">Diagnostic Wax up</option>
                                    <option value="Study Model">Study Model</option>
                                    <option value="Mouth Guard">Mouth Guard</option>
                                    <option value="Night Guard">Night Guard</option>
                                    <option value="Essix Retainer">Essix Retainer</option>
                                    <option value="Bleaching Trays">Bleaching Trays</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="lg:col-span-3">
                            <label class="block font-medium text-gray-700 mb-2" for="${baseName}[arch]">Arch</label>
                            <div class="select-wrapper relative mb-3 w-full">
                                <select name="${baseName}[arch]" class="select-other w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select</option>
                                    <option value="Top">Top</option>
                                    <option value="Bottom">Bottom</option>
                                    <option value="Both">Both</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>`;
                }
        }

        $(container).empty();

        $(container).append(`<div class="lt lt-${treatmentType.toLowerCase()} grid grid-cols-1 lg:grid-cols-12 gap-6">
            <div class="lg:col-span-3">
                <div class="bg-gradient-to-br from-gray-800 to-gray-900 text-white rounded-lg p-6 h-48 flex flex-col shadow-md justify-end">
                    <div class="text-gray-400 text-3xl font-light opacity-60 tracking-wider text-right">${treatmentType.toUpperCase()} ${optionName.toUpperCase()}</div>
                </div>
            </div>
            ${fields}

            <div class="lg:col-span-3 ai-notes">
              <label class="block font-medium text-gray-700 mb-2" for="${baseName}[further_information]">Notes</label>
              <div class="relative group">
                  <div class="border bg-card text-card-foreground p-0 overflow-hidden border-[#e5e5e5] rounded-xl transition-all duration-200 shadow-sm hover:shadow-md" data-v0-t="card">
                    <div class="absolute top-0 left-0 w-full h-1.5 opacity-80 rounded-tl-xl rounded-tr-xl" style="background: linear-gradient(to right, rgb(167, 243, 208), rgb(110, 231, 183));"></div>
                    <div class="pt-3 bg-white"></div>
                    <textarea class="ai-textarea flex w-full rounded-md border-input bg-background py-2 ring-offset-background placeholder:text-muted-foreground
                    focus-visible:outline-none focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50
                    min-h-[120px] border-0 shadow-none resize-none px-4 pt-1 pb-10 focus-visible:ring-0 text-sm leading-relaxed" rows="5" name="${baseName}[further_information]">${data["further_information"] || ""}</textarea>
                    
                    <div class="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-white via-white to-transparent ai-input-btns">
                      <div class="flex items-center justify-end">
                        <div class="flex items-center gap-2">
                          <div class="animation-wave hidden relative flex items-center h-8 px-3 py-1 mr-1 animate-fadeIn overflow-hidden rounded-xl backdrop-blur-md bg-[rgba(239,68,68,0.08)] border border-[rgba(239,68,68,0.2)] shadow-[0_0_12px_rgba(239,68,68,0.15)]">
                            <div class="absolute inset-0 bg-gradient-to-r from-[rgba(239,68,68,0.02)] to-[rgba(239,68,68,0.08)]"></div>
                            <div class="relative flex items-end justify-center h-4 gap-[3px] z-10">
                              <div class="rounded-full" style="width: 2px; height: 35%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.1s infinite alternate none running soundWave;"></div>
                              <div class="rounded-full" style="width: 1.5px; height: 60%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.3s infinite alternate none running soundWave;"></div>
                              <div class="rounded-full" style="width: 2px; height: 90%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0s infinite alternate none running soundWave;"></div>
                              <div class="rounded-full" style="width: 1.5px; height: 50%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.2s infinite alternate none running soundWave;"></div>
                              <div class="rounded-full" style="width: 2px; height: 75%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.15s infinite alternate none running soundWave;"></div>
                              <div class="rounded-full" style="width: 1.5px; height: 100%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.05s infinite alternate none running soundWave;"></div>
                              <div class="rounded-full" style="width: 2px; height: 65%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.25s infinite alternate none running soundWave;"></div>
                              <div class="rounded-full" style="width: 1.5px; height: 85%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.1s infinite alternate none running soundWave;"></div>
                              <div class="rounded-full" style="width: 2px; height: 45%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.3s infinite alternate none running soundWave;"></div>
                              <div class="rounded-full" style="width: 1.5px; height: 70%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.2s infinite alternate none running soundWave;"></div>
                            </div>
                            <div class="ml-2 text-[10px] font-medium text-[#ef4444] tracking-wide opacity-80">
                              RECORDING
                            </div>
                          </div>
                          <button type="button" class="ai-record inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background
                                        focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none
                                        disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground
                                        rounded-full relative overflow-hidden transition-all duration-300 h-9 w-9 bg-gradient-to-b from-[#fef2f2] to-[#fee2e2] border border-[#fecaca]
                                        shadow-sm text-[#ef4444] hover:shadow-md hover:from-[#fee2e2] hover:to-[#fecaca] active:scale-95 backdrop-blur-sm" aria-label="Start recording" title="Start recording">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mic h-4 w-4">
                              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                              <line x1="12" x2="12" y1="19" y2="22"></line>
                            </svg>
                          </button>
                          <button data-edit="improve the text" class="ai-pill-button improve-btn inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background
                                        focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none
                                        disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground
                                        h-9 px-3 rounded-full bg-gradient-to-b from-[#fff7ed] to-[#ffedd5] border border-[#fed7aa] shadow-sm text-[#f97316] gap-1.5 transition-all
                                        duration-200 hover:shadow-md hover:from-[#ffedd5] hover:to-[#fed7aa] active:scale-95">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles h-4 w-4">
                              <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>
                              <path d="M20 3v4"></path>
                              <path d="M22 5h-4"></path>
                              <path d="M4 17v2"></path>
                              <path d="M5 18H3"></path>
                            </svg>
                            <span class="text-xs font-medium">Improve</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            </div>
        </div>
        `);

        const keys = [
            "materials", "stage", "treatment_type", "abutment_material", "arch",
            "implant_system", "margin", "num_wings", "post_core",
            "post_core_materials", "retention", "units"
        ];

        keys.forEach((key) => {
            const selector = `select[name$="${baseName}[${key}]"]`;
            const $select = $(selector);
            const value = data[key];

            if (!$select.length) return;

            const hasOption = $select.find(`option[value="${value}"]`).length > 0;

            if (hasOption) {
                $select.val(value);
            } else if (value) {
                const $wrapper = $select.closest('.select-wrapper');

                const $input = $(`
                  <input type="text"
                         name="${baseName}[${key}]"
                         value="${value}"
                         class="w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                `);

                const $closeBtn = $(`
                  <button type="button"
                          class="absolute right-2 top-2 text-gray-500 hover:text-red-500 font-bold text-lg">
                    ✕
                  </button>
                `);

                $closeBtn.on('click', function () {
                    $wrapper.empty().append($select);
                    $select.val('');
                });

                $wrapper.empty().append($input).append($closeBtn);
            }
        });


        // $(`select[name$="${baseName}[materials]"]`).val(data['materials']);
        // $(`select[name$="${baseName}[stage]"]`).val(data['stage']);
        // $(`select[name$="${baseName}[treatment_type]"]`).val(data['treatment_type']);
        // $(`select[name$="${baseName}[abutment_material]"]`).val(data['abutment_material']);
        // $(`select[name$="${baseName}[arch]"]`).val(data['arch']);
        $(`input[name$="${baseName}[implant_system]"]`).val(data['implant_system']);
        // $(`select[name$="${baseName}[margin]"]`).val(data['margin']);
        // $(`select[name$="${baseName}[num_wings]"]`).val(data['num_wings']);
        // $(`select[name$="${baseName}[post_core]"]`).val(data['post_core']);
        // $(`select[name$="${baseName}[post_core_materials]"]`).val(data['post_core_materials']);
        // $(`select[name$="${baseName}[retention]"]`).val(data['retention']);
        // $(`select[name$="${baseName}[units]"]`).val(data['units']);

        document.addEventListener('change', function (e) {
            if (e.target.matches('select.select-other')) {
                const select = e.target;
                if (select.value === 'Other') {
                    const wrapper = select.closest('.select-wrapper');
                    const name = select.getAttribute('name');

                    const input = document.createElement('input');
                    input.type = 'text';
                    input.name = name;
                    input.placeholder = 'Enter other value';
                    input.className = 'w-full bg-white border border-gray-300 rounded-lg p-2 h-10 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200';

                    const closeButton = document.createElement('button');
                    closeButton.type = 'button';
                    closeButton.innerHTML = '✕';
                    closeButton.className = 'absolute right-2 top-2 text-gray-500 hover:text-red-500 font-bold text-lg';
                    closeButton.addEventListener('click', function () {
                        wrapper.innerHTML = '';
                        wrapper.appendChild(select);
                        select.value = '';
                    });

                    wrapper.innerHTML = '';
                    wrapper.appendChild(input);
                    wrapper.appendChild(closeButton);
                }
            }
        });

        $(".ai-textarea").each(function () {
            aiInput(this);
        });

        $('.ai-record').on('click', function (e) {
            changeMicIcon($(this));

            var btnContainer = $(this).closest('.ai-input-btns');
            btnContainer.find('.animation-wave').toggleClass('hidden');
        });
    }

    function addListeners(item, existingTab = false, amend = false) {
        if (existingTab) {
            $(item).find(".teethcheckbox:checked").each(function () {
                const $checkbox = $(this);
                const $label = $("label[for='" + $checkbox.attr("id") + "']");
                $label.addClass("testclass");
            });

            $(item).find(".custom-checkbox").each(function () {
                if ($(this).find("input[type='checkbox']").prop("checked")) {
                    $(this).addClass("checked");
                }
            });
        }

        setInterval(function () {
            $(item).find('#imagedraw').prev().prev().val($(item).find('#imagedraw').get(0).currentSrc)
        }, 100);

        setInterval(function () {
            $(item).find('#imagedrawshade').prev().prev().val($(item).find('#imagedrawshade').get(0).currentSrc)
        }, 100);

        $(item).find(".ldp").each(function () {
            aiInput(this);
        });

        if (amend) return;

        $(item).find(".teethcheckbox").change(function () {
            const $checkbox = $(this);
            const $label = $("label[for='" + $checkbox.attr("id") + "']");

            if ($checkbox.is(":checked")) {
                $label.addClass("testclass");
            } else {
                $label.removeClass("testclass");
            }
        });


        $(item).find(".custom-checkbox").click(function () {
            $(this).toggleClass("checked");
            $(this).find("input[type='checkbox']").prop("checked", $(this).hasClass("checked"));
        })

        $(item).find("#drawicon1").click(function () {
            window.showMarkerArea(this.closest(".ldi").querySelector("#imagedraw"));
        })

        $(item).find("#drawicon2").click(function () {
            window.showMarkerArea(this.closest(".ldi").querySelector("#imagedrawshade"));
        })

        $(item).find(".ldt").each(function () {
            renderTreatment($(this).find(".treatment-container"), $(this).find(".field-lab-item").get(0).selectedOptions[0]);
            $(this).find(".field-lab-item").change(function () {
                const days = Math.max(...$(".field-lab-item").map(function () {
                    ($(this).get(0).selectedOptions[0] ? Number($(this).get(0).selectedOptions[0].dataset["returnTime"]) : null) || 14
                }).get(), 14);
                $("#date_due").val(new Date(new Date().setDate(new Date().getDate() + days)).toISOString().split("T")[0]);
                renderTreatment($(this).closest(".ldt").find(".treatment-container"), this.selectedOptions[0]);
            });
        })

        $(item).find(".ldt-container").on("cocoon:after-insert", function (e, insertedItem) {
            console.log('ABC')
            renderTreatment(insertedItem.find(".treatment-container"), $(insertedItem).find(".field-lab-item").get(0).selectedOptions[0]);
            $(insertedItem).find(".field-lab-item").change(function () {
                const days = Math.max(...$(".field-lab-item").map(function () {
                    ($(this).get(0).selectedOptions[0] ? Number($(this).get(0).selectedOptions[0].dataset["returnTime"]) : null) || 14
                }).get(), 14);
                $("#date_due").val(new Date(new Date().setDate(new Date().getDate() + days)).toISOString().split("T")[0]);
                renderTreatment($(insertedItem).find(".treatment-container"), this.selectedOptions[0]);
            });
        });

        if (!existingTab) $(item).find(".add-treatment").click();

        $('.ldt-remove-button').click(function () {
            const timestamp = $(this).closest('.nested-fields.ldi').find('input[id*="lab_docket_lab_docket_items_attributes"]').attr('id')?.match(/\d+/)?.[0];
            const destroyInput = $(`input[id="lab_docket_lab_docket_items_attributes_${timestamp}__destroy"]`);
            destroyInput.val('1');

            const $ldi = $(this).closest('.nested-fields.ldi');
            const ldiId = $ldi.attr('id');

            $ldi.hide();
            $(`#item_tabs [data-target="#${ldiId}"]`).hide();
        });
    }

    function insertTab(item, existingTab = false, i = 0, amend = false) {
        if ($(item).hasClass("ldi")) {
            if (!existingTab) i = $("#lab_docket_items .ldi").length;

            const tabId = `item_tab_${i}`;
            $(item).attr("id", tabId);

            const tabButton = $(`
                <li>
                    <button class="item-tab px-6 py-3 bg-gradient-to-b from-blue-50 to-blue-100 text-blue-700 rounded-xl overflow-hidden shadow-sm border border-blue-200 transition-all duration-300 hover:shadow hover:from-blue-100 hover:to-blue-200" 
                    data-target="#item_tab_${i}" 
                    type="button" >Treatment
                    </button>
                </li>`);

            $("#item_tabs").append(tabButton);

            tabButton.find("button").on("click", function () {
                switchTab($(this).data("target"));
            });

            if (!existingTab) tabButton.find("button").click();
            addListeners(item, existingTab, amend);
            if (amend) amendTab(item);
        }
    }

    $("#lab_docket_items .ldi").each((i, item) => insertTab(item, true, i));
    $("#lab_docket_items .ldt").each((i, item) => insertTab(item, true, i));

    function addAiListeners() {
        $(".ai-prescription-editor").click(function () {
            fetch("/admin/conversations/ai_edit", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({edits: $(this).text(), message: $('#prescription-editor').val()})
            }).then(r => r.json()).then(j => {
                $('#prescription-editor').val(j.message);
            });
        })

        $(".ai-further-prescription-editor").click(function () {
            fetch("/admin/conversations/ai_edit", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({edits: $(this).text(), message: $('#prescription-editor').val()})
            }).then(r => r.json()).then(j => {
                $('#further-prescription-editor').val(j.message);
            });
        })
    }

    $("#lab_docket_items").on("cocoon:after-insert", function (e, insertedItem) {
        insertTab(insertedItem);
        addAiListeners();
    });

    $("#lab_docket_items").on("cocoon:after-remove", function (e, removedItem) {
        $(`#item_tabs [data-target="#${removedItem.attr("id")}"]`).closest("li").remove();
    });

    $("#new_lab_docket").on("submit", function () {
        $("#lab_docket_items :disabled").prop("disabled", false);
        return true;
    });

    if ($("#lab_docket_items .ldi").length === 0) {
        $("#item_tabs .add_fields").click();
    }

    $('.ldt-remove-button').click(function () {
        const timestamp = $(this).closest('.nested-fields.ldi').find('input[id*="lab_docket_lab_docket_items_attributes"]').attr('id')?.match(/\d+/)?.[0];
        const destroyInput = $(`input[id="lab_docket_lab_docket_items_attributes_${timestamp}__destroy"]`);
        destroyInput.val('1');

        const $ldi = $(this).closest('.nested-fields.ldi');
        const ldiId = $ldi.attr('id');

        $ldi.hide();
        $(`#item_tabs [data-target="#${ldiId}"]`).hide();
    });
})
