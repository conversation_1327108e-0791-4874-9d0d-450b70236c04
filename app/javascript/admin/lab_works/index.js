import filterController from '../crm/modules/filterController';
import Sortable from 'sortablejs';

$(document).ready(function () {
    if (!$('.lab-works-container').length) return;

    const isPatientMode = document.querySelector('.lab-works-container')?.dataset.patientMode === 'true';

    if (document.getElementById('filter-modal')) {
        filterController.init();
    }

    initDragAndDrop(isPatientMode);

    document.querySelectorAll('.lw-actions-toggle').forEach(button => {
        button.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();

            const dropdownId = this.getAttribute("data-dropdown-toggle");
            const dropdown = document.getElementById(dropdownId);

            document.querySelectorAll(".lw-actions-menu").forEach(menu => {

                if (menu !== dropdown) {
                    menu.classList.add("hidden");
                } else {
                    menu.classList.remove("hidden");
                }
            });
        });
    });

    document.addEventListener('click', function(event) {
        if (!event.target.closest(".lw-actions-toggle") && !event.target.closest(".lw-actions-menu")) {
            document.querySelectorAll(".lw-actions-menu").forEach(menu => {
                menu.classList.add("hidden");
            });
        }
    });

    $('#lw-patients-search').on('keyup', function () {
        let query = $(this).val();
        $(".lab-works-container .lab-work-card").each(function () {
            const title = $(this).find(".patient-name");

            if (title.text().toLowerCase().search(query.toLowerCase()) !== -1) {
                $(this).show();
            } else {
                $(this).hide();
            }

            updateCounter()
        })
    });

    const $focusBtn = $('.focus-on-me-btn');
    const $cards = $('.lab-work-card');

    $focusBtn.on('click', function () {
        const practitionerId = $(this).data('practitioner-id');
        const isActive = $(this).hasClass('active');

        if (isActive) {
            $(this).removeClass('active ring-2 ring-indigo-500 bg-indigo-50');
            $cards.each(function () {
                this.style.display = '';
            });
        } else {
            $(this).addClass('active ring-2 ring-indigo-500 bg-indigo-50');
            $cards.each(function () {
                const cardPractitionerId = $(this).data('practitioner-id');
                if (cardPractitionerId === practitionerId) {
                    this.style.display = '';
                } else {
                    this.style.display = 'none';
                }
            });
        }

        updateCounter();
    });

    function updateCounter() {
        const lists = document.querySelectorAll('.column-container');

        lists.forEach(list => {
            const cards = list.querySelectorAll('.lab-work-card');
            let visibleCount = 0;

            cards.forEach(card => {
                if (getComputedStyle(card).display !== 'none') {
                    visibleCount++;
                }
            });

            const countElement = list.querySelector('.card-count');
            if (countElement) {
                countElement.textContent = visibleCount.toString();
            }
        });
    }
})

function initDragAndDrop(isPatientMode) {
    const columns = document.querySelectorAll('.column-dropzone');
    const dropZones = document.querySelectorAll('.drop-zone');

    columns.forEach(column => {
        new Sortable(column, {
            group: {
                name: 'lab-works',
                pull: true,
                put: true
            },
            sort: false,
            draggable: '.lab-work-card',
            animation: 150
        });
    });

    dropZones.forEach(dropZone => {
        new Sortable(dropZone, {
            group: {
                name: 'lab-works',
                pull: false,
                put: true
            },
            draggable: '.lab-work-card',
            animation: 150,
            onMove(evt) {
                const sourceTitle = evt.dragged.getAttribute('data-source-title');
                const dropTitle = evt.to.getAttribute('data-drop-title');

                if (sourceTitle === 'Ready For Appointment' && dropTitle === 'Fitted Successfully - Auto Archive') {
                    return true;
                } else if (sourceTitle === 'Sent To Lab' && dropTitle === 'Arrived At Practice - Dentist Check Required') {
                    return true;
                } else if (sourceTitle === 'Waiting For Quality Check' && dropTitle === 'Checked By Dentist - Ready For Patient') {
                    return true;
                } else if (sourceTitle === 'Error or Adjustment Required' && dropTitle === 'Archive Case - Not proceeding') {
                    return true;
                } else if (dropTitle === 'Archive Case - Not proceeding' || dropTitle === 'Fitted Successfully - Auto Archive') {
                    return true;
                }

                return false;
            },
            onAdd(evt) {
                const labWorkId = evt.item.getAttribute('data-lab-work-id');
                const toTitle = evt.to.getAttribute('data-drop-title');

                fetch(`/admin/lab_works/${labWorkId}/update_status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ status: toTitle, source: isPatientMode })
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not OK');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.redirect_url) {
                            window.location.href = data.redirect_url;
                        } else {
                            alert(data.message || 'Status update failed');
                        }
                    })
                    .catch(error => {
                        console.error('Error updating status:', error);
                        alert('An unexpected error occurred.');
                    });
            }
        });
    });
}
