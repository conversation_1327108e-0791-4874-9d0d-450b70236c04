$(document).ready(function() {
    setTimeout(function () {
        $(".ai-textarea").each(function () {
            aiInput(this);
        });

        $('.ai-record').on('click', function (e) {
            changeMicIcon($(this));

            var btnContainer = $(this).closest('.ai-input-btns');
            btnContainer.find('.animation-wave').toggleClass('hidden');
        });
    }, 100);

    function aiInput(elem, inputElem) {
        let originalText = '';
        let finalTranscript = '';
        let interimTranscript = '';
        if (!inputElem) inputElem = elem;

        const editorId = $(elem).attr('id');
        const editor = tinymce.get(editorId);

        const rec = $(elem).closest(".ai-notes").find(".ai-input-btns .ai-record");

        $(rec).click(() => {
            if ($(rec).hasClass('recording_enabled')) {
                stopTranscriptionInput();
            } else {
                startTranscriptionInput();
            }
        })

        async function startTranscriptionInput() {
            originalText = editor ? editor.getContent({ format: 'text' }) : $(elem).val();
            finalTranscript = '';
            interimTranscript = '';
            $(rec).addClass('recording_enabled');

            const stream = await navigator.mediaDevices.getUserMedia({audio: true});
            mediaRecorderTranscription = new MediaRecorder(stream, {mimeType: 'audio/webm'});

            socketTranscription = new WebSocket('wss://api.deepgram.com/v1/listen?model=nova-3-medical&language=en-US&punctuate=true&interim_results=true&endpointing=500', ['token', 'ebe685557cc8a3fec4f58f28dd34efc8c247b75c']);

            socketTranscription.onopen = function () {
                mediaRecorderTranscription.addEventListener('dataavailable', event => {
                    if (event.data.size > 0 && socketTranscription.readyState === 1) {
                        socketTranscription.send(event.data);
                    }
                });

                mediaRecorderTranscription.start(250);  // Record in chunks
            };

            socketTranscription.onmessage = function (message) {
                const received = JSON.parse(message.data);
                const transcript = received.channel.alternatives[0]?.transcript;

                if (transcript) {
                    if (received.is_final) {
                        finalTranscript += ' ' + transcript;
                        interimTranscript = '';
                    } else {
                        interimTranscript = transcript;
                    }

                    updateTranscriptionText();
                }
            };
        }

        function stopTranscriptionInput() {
            $(rec).removeClass('recording_enabled');

            if (mediaRecorderTranscription) mediaRecorderTranscription.stop();
            if (socketTranscription) socketTranscription.close();

            // Stop all tracks on the stream
            if (mediaRecorderTranscription.stream) {
                mediaRecorderTranscription.stream.getTracks().forEach(track => track.stop());
            }
        }

        function updateTranscriptionText() {
            const updatedText = `${originalText} ${finalTranscript} ${interimTranscript}`.trim();

            if (editor) {
                editor.setContent(`<p>${updatedText.replace(/\n/g, '</p><p>')}</p>`);
            } else {
                $(elem).val(updatedText);
                $(elem).scrollTop($(elem)[0].scrollHeight);
            }
        }
    }

    window.aiInput = aiInput
});
