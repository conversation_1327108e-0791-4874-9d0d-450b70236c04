import filterController from '../crm/modules/filterController';
import Sortable from 'sortablejs';

$(document).ready(function () {
  if (!$('.secure-send-container').length) return;

  if (document.getElementById('filter-modal')) {
    filterController.init();
  }

  initDragAndDrop();

  document.querySelectorAll('.sr-actions-toggle').forEach(button => {
    button.addEventListener('click', function(event) {
      event.preventDefault();
      event.stopPropagation();

      const dropdownId = this.getAttribute("data-dropdown-toggle");
      const dropdown = document.getElementById(dropdownId);

      document.querySelectorAll(".sr-actions-menu").forEach(menu => {
        if (menu !== dropdown) {
          menu.classList.add("hidden");
        } else {
          menu.classList.remove("hidden");
        }
      });
    });
  });

  document.addEventListener('click', function(event) {
    if (!event.target.closest(".sr-actions-toggle") && !event.target.closest(".sr-actions-menu")) {
      document.querySelectorAll(".sr-actions-menu").forEach(menu => {
        menu.classList.add("hidden");
      });
    }
  });

  const $focusBtn = $('.focus-on-me-btn');
  const $cards = $('.request-card');

  $focusBtn.on('click', function () {
    const practitionerId = $(this).data('practitioner-id');
    const isActive = $(this).hasClass('active');

    if (isActive) {
      $(this).removeClass('active ring-2 ring-indigo-500 bg-indigo-50');
      $cards.each(function () {
        this.style.display = '';
      });
    } else {
      $(this).addClass('active ring-2 ring-indigo-500 bg-indigo-50');
      $cards.each(function () {
        const cardPractitionerId = $(this).data('practitioner-id');
        if (cardPractitionerId === practitionerId) {
          this.style.display = '';
        } else {
          this.style.display = 'none';
        }
      });
    }

    updateCounter();
  });

  function updateCounter() {
    const lists = document.querySelectorAll('.column-container');

    lists.forEach(list => {
      const cards = list.querySelectorAll('.request-card');
      let visibleCount = 0;

      cards.forEach(card => {
        if (getComputedStyle(card).display !== 'none') {
          visibleCount++;
        }
      });

      const countElement = list.querySelector('.card-count');
      if (countElement) {
        countElement.textContent = visibleCount.toString();
      }
    });
  }

  //
  // document.querySelector("#signDocumentModal").addEventListener("show.bs.modal", e => {
  //   const url = e.relatedTarget.dataset.url;
  //   document.querySelector("#sign_if").src = url;
  //   document.querySelector("#sign_loader").style.display = "flex";
  // });
  //
  // document.querySelector("#signDocumentModal").addEventListener("hidden.bs.modal", e => {
  //   document.querySelector("#sign_if").src = "";
  // });
  //
  // document.querySelector("#sign_if").addEventListener("load", () => {
  //   document.querySelector("#sign_loader").style.display = "none";
  // });

  const signInPersonModal = document.getElementById("signInPersonModal");

  if (signInPersonModal) {
    document.querySelectorAll("[data-person-modal-target]").forEach((button) => {
      button.addEventListener("click", (event) => {
        const url = signInPersonModal.getAttribute("data-url");
        const container = signInPersonModal.querySelector("#qrCodeContainer");

        signInPersonModal.classList.remove("hidden");

        container.innerHTML = `
        <div class="flex items-center justify-center h-[150px]">
          <i class="fas fa-spinner fa-spin text-2xl"></i>
          <span class="ml-4">Loading QR code...</span>
        </div>
      `;

        // Give a moment to render loading UI before generating QR code
        setTimeout(() => {
          container.innerHTML = "";
          new QRCode(container, {
            text: url,
            width: 250,
            height: 250,
            correctLevel: QRCode.CorrectLevel.H
          });
        }, 100);
      });
    });

    signInPersonModal.querySelector("[data-modal-close]").addEventListener("click", () => {
      signInPersonModal.classList.add("hidden");
    });

    // Optional: close modal on click outside
    signInPersonModal.addEventListener("click", (e) => {
      if (e.target === signInPersonModal) {
        signInPersonModal.classList.add("hidden");
      }
    });
  }
});

function initDragAndDrop() {
  const columns = document.querySelectorAll('.column-dropzone');
  const dropZones = document.querySelectorAll('.drop-zone');

  columns.forEach(column => {
    new Sortable(column, {
      group: {
        name: 'signature-requests',
        pull: true,
        put: true
      },
      sort: false,
      draggable: '.request-card',
      animation: 150
    });
  });

  dropZones.forEach(dropZone => {
    new Sortable(dropZone, {
      group: {
        name: 'signature-requests',
        pull: false,
        put: true
      },
      draggable: '.request-card',
      animation: 150,
      onMove(evt) {
        const sourceTitle = evt.dragged.getAttribute('data-source-title');
        const dropTitle = evt.to.getAttribute('data-drop-title');

        if (sourceTitle === 'Signed - Awaiting Staff Check' && dropTitle === 'closed') {
          return true;
        } else if (dropTitle === 'archived') {
          return true;
        }

        return false;
      },
      onAdd(evt) {
        const requestId = evt.item.getAttribute('data-request-id');
        const toTitle = evt.to.getAttribute('data-drop-title');

        fetch(`/admin/signature_requests/${requestId}/update_status`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          },
          body: JSON.stringify({ signature_request: { status: toTitle } })
        })
            .then(response => {
              if (!response.ok) {
                throw new Error('Network response was not OK');
              }
              return response.json();
            })
            .then(data => {
              if (data.success && data.redirect_url) {
                window.location.href = data.redirect_url;
              } else {
                alert(data.message || 'Status update failed');
              }
            })
            .catch(error => {
              console.error('Error updating status:', error);
              alert('An unexpected error occurred.');
            });
      }
    });
  });
}
