$(document).ready(function() {
    if(!$('.tp-oral-health-step').length) return;

    waitForTinyMCE("treatment_plan_option_oral_health_notes", function(editor) {
        console.log("TinyMCE editor ready:", editor);

        createBaseSummary(editor, 'base');
        loadCompletedTreatments();
        loadHistoryTreatments();

        startOverlay(editor);

        // $('.improve-btn').on('click', function () {
        //     startOverlay(editor);
        // });
    });
});

function createBaseSummary(health_notes, type, cotId = null) {
    fetch(`/admin/treatment_plan_options/create_summary`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            patient_id: window._patientId,
            type: type,
            course_of_treatment_id: cotId
        })
    })
        .then(response => response.json())
        .then(data => {
            // health_notes.value = data.summary;
            health_notes.setContent(data.summary);
            const $overlay = $('#overlay');
            $overlay.fadeOut(300);
        })
        .catch(error => {
            console.error('Error getting summary:', error);
        })
        .finally(() => {
            // health_notes.disabled = false;
        })
}

function startOverlay(health_notes) {
    const $overlay = $('.tp-oral-health #overlay');
    const $progressFill = $('.tp-oral-health #progressFill');

    $overlay.css('display', 'flex').addClass('show');
    $progressFill.css('width', '0%');

    let progress = 0;
    const interval = setInterval(() => {
        progress += 0.5;
        $progressFill.css('width', `${progress}%`);
        if (progress >= 100) {
            clearInterval(interval);
            $overlay.fadeOut(300);
            health_notes.disabled = false;
        }
    }, 100);
}

function waitForTinyMCE(id, callback, attempts = 10, delay = 200) {
    const editor = tinymce.get(id);
    if (editor) {
        callback(editor);
    } else if (attempts > 0) {
        setTimeout(() => {
            waitForTinyMCE(id, callback, attempts - 1, delay);
        }, delay);
    } else {
        console.warn("TinyMCE editor not found:", id);
    }
}

window.createBaseSummary = createBaseSummary;
