import modalUtils from "../crm/modal";

$(document).ready(function () {
    let $detailsDiv = $(".treatment-plan-details");
    if (!$detailsDiv.length) return;

    initTabs();
    initializePlanOptionModal();
    $detailsDiv.data("imported", false);
    $('.aqtp-edit-button').click(initializePlanOptionModal);

    waitForTinyMCE("treatment_plan_option_proposed_course_of_treatment_notes", function(editor) {
        createBaseSummary(editor, 'treatment_plan', $detailsDiv.data('course-of-treatment-id'));
        startOverlay(editor);
        updateTreatmentPlanOptionTotals();
        loadCompletedTreatments();

        // $('.improve-btn').on('click', function () {
        //     startOverlay(editor);
        // });
    })
})

function initTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanels = document.querySelectorAll('.tab-panel');

    tabButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetId = btn.getAttribute('data-tab-target');

            // Deselect all buttons
            tabButtons.forEach(b => {
                b.setAttribute('aria-selected', 'false');
                b.classList.remove('bg-blue-100', 'text-blue-700');
            });

            // Hide all panels
            tabPanels.forEach(panel => {
                panel.hidden = true;
            });

            // Activate selected tab
            btn.setAttribute('aria-selected', 'true');
            document.getElementById(targetId).hidden = false;
        });
    });

    // Show default tab
    const defaultBtn = document.querySelector('.tab-btn[aria-selected="true"]');
    if (defaultBtn) {
        document.getElementById(defaultBtn.getAttribute('data-tab-target')).hidden = false;
    }
}

function calculateDiscountedPrice(treatmentId) {
    const overridePriceField = document.querySelector(`.override_price_${treatmentId}`);
    const discountAmountField = document.getElementById(`discount_amount_${treatmentId}`);
    const discountedPriceField = document.getElementById(`override_price_${treatmentId}`);
    const discountedTypeField = document.getElementById(`discount_type_${treatmentId}`);

    const overridePrice = parseFloat(overridePriceField.textContent) || 0;
    const discountAmount = parseFloat(discountAmountField.value) || 0;

    if (overridePrice > 0) {
        if (discountedTypeField.value === 'Percentage') {
            const discountedPrice = overridePrice - (overridePrice * discountAmount / 100);
            discountedPriceField.value = discountedPrice.toFixed(2);
        } else {
            const discountedPrice = overridePrice - discountAmount;
            discountedPriceField.value = discountedPrice.toFixed(2);
        }
    }
    updateTreatmentPlanOptionTotals()
}

function toggleDiscountMenu(id) {
    const popout = document.querySelectorAll(`tr.treatment-row[data-charted-treatment-id="${id}"]`);
    popout.forEach(el => el.classList.toggle('hidden'));
}

function initializePlanOptionModal() {
    let $detailsDiv = $(".treatment-plan-details");
    const imported = $detailsDiv.data("imported");
    if (imported) return;

    const clinicians = $detailsDiv.data("clinicians");
    const cots = $detailsDiv.data("cots");
    const categories = $detailsDiv.data("categories");
    const title = $detailsDiv.data("title");
    const optionId = $detailsDiv.data("optionId");
    const clinicianId = clinicians.find(clinician => clinician.selected === true)?.id;
    const cotId = cots.find(cot => cot.selected === true)?.id;
    const categoryId = categories.find(category => category.selected === true)?.id;

    const optionNameHtml = `
                <p class="mb-0" style="font-size: 14px; font-weight: 400; text-align: left; margin-top: 10px;">Treatment Plan Option Name</p>
                <input type="text" id="treatmentPlanOptionName" style="padding: 14px; height: 46px; width: 100% !important; font-size: 16px !important; border: 1px solid #d9d9d9; border-radius: 8px;" placeholder="Treatment Plan Option Name">
            `;

    const cotCategoryHtml = `
                <p style="font-size: 14px; font-weight: 400; text-align: left; margin-top: 10px;">COT Category</p>
                <select id="cot_category_dropdown" style="margin: 5px 8px 15px 8px; width: 100%;" ${categories.length === 1 ? 'disabled' : ''}>
                    ${categories.map(category => `<option value="${category.id}" selected="${category.selected}">${category.name}</option>`).join('')}
                </select>
            `;

    const courseOfTreatmentHtml = `
                <p style="font-size: 14px; font-weight: 400; text-align: left; margin-top: 10px;">Course of Treatment</p>
                <select id="cot_dropdown" style="margin: 5px 8px 15px 8px; width: 100%;" ${cots.length === 1 ? 'disabled' : ''}>
                    ${cots.map(cot => `<option value="${cot.id}" data-cot-category-id="${cot.cot_category_id}" data-clinician-id="${cot.main_clinician_id}" selected="${cot.selected}">${cot.name}</option>`).join('')}
                </select>
            `;

    const dropdownCliniciansHtml = `
                <p style="font-size: 14px; font-weight: 400; text-align: left; margin-top: 20px;">Main Clinician</p>
                <select id="main_clinician_dropdown" class="swal2-select" style="margin: 5px 8px 15px 8px; width: 100%;" ${clinicians.length === 1 ? 'disabled' : ''}>
                    ${clinicians.map(user => `<option value="${user.id}" selected="${user.selected}">${user.name}</option>`).join('')}
                </select>
                `;

    modalUtils.showModal({
        title: 'Add New Treatment Option',
        content: `
                    ${optionNameHtml}
                    ${courseOfTreatmentHtml}
                    ${cotCategoryHtml}
                    ${dropdownCliniciansHtml}
                `,
        showCancelButton: false,
        confirmButtonText: 'Import Course of Treatment',
        confirmButtonColor: 'bg-green-300 hover:bg-sky-400',
        html: true,
        focusConfirm: false,
        allowOutsideClick: false,
        allowEscapeKey: false,
        onOpen: () => {
            $('#cot_category_dropdown').select2({
                selectionCssClass: "form-control",
                dropdownParent: $('.modal-dialog-container'),
                allowClear: false
            });

            $('#cot_dropdown').select2({
                selectionCssClass: "form-control",
                dropdownParent: $('.modal-dialog-container'),
                allowClear: false
            });

            $('#main_clinician_dropdown').select2({
                selectionCssClass: "form-control",
                dropdownParent: $('.modal-dialog-container'),
                placeholder: "Select a team member",
                allowClear: false
            });

            $('#cot_dropdown').on('change', function () {
                const selectedOption = $(this).find('option:selected');
                const cotCategoryId = selectedOption.data('cot-category-id');
                const clinicianId = selectedOption.data('clinician-id');

                $('#cot_category_dropdown').val(cotCategoryId).trigger('change');
                $('#main_clinician_dropdown').val(clinicianId).trigger('change');
            });

            if (title) {
                $('#treatmentPlanOptionName').val(title);
                $('#treatmentPlanOptionName').each((index, elem) => {
                    elem.style.zIndex = '1100';
                });
            }

            if (clinicianId) {
                $('#main_clinician_dropdown').val(clinicianId).trigger('change');
            }

            if (cotId) {
                $('#cot_dropdown').val(cotId).trigger('change');
            }

            if (categoryId) {
                $('#cot_category_dropdown').val(categoryId).trigger('change');
            }

            $('.select2-selection__arrow').each((index, elem) => {
                elem.style.display = 'none';
            });

            $('.select2.select2-container.select2-container--default').each((index, elem) => {
                elem.style.margin = '0 auto';
            });

            $('.select2-container').each((index, elem) => {
                elem.style.zIndex = '1100';
            });

            $('.select2-results__option.select2-results__option--selectable').each((index, elem) => {
                elem.style.zIndex = '1100';
            });

            $('.select2-selection__rendered').each((index, elem) => {
                elem.style.paddingLeft = '0';
                elem.style.marginTop = '0';
                elem.style.textAlign = 'left';
            });
            $('#select2-main_clinician_dropdown-container')[0].style.textAlign = 'left';
        },
        preConfirm: () => {
            const selectedCOT =  document.querySelector('.modal-dialog-block #cot_dropdown').value;
            const selectedCategory =  document.querySelector('.modal-dialog-block #cot_category_dropdown').value;
            const selectedClinician =  document.querySelector('.modal-dialog-block #main_clinician_dropdown').value;
            const optionName =  document.querySelector('.modal-dialog-block #treatmentPlanOptionName').value;

            if (!selectedCOT) {
                Swal.showValidationMessage('Please select a Course of Treatment');
            }

            if (!selectedCategory) {
                Swal.showValidationMessage('Please select a COT Category');
            }

            if (!selectedClinician) {
                Swal.showValidationMessage('Please select a team member');
            }

            return {optionName, selectedCategory, selectedCOT, selectedClinician};
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/admin/treatment_plan_options/${optionId}/import_cot`,
                type: 'PATCH',
                headers: {"X-CSRF-Token": $('meta[name="csrf-token"]').attr("content")},
                dataType: 'json',
                data: {
                    treatment_plan_option: {
                        main_clinician_id: result.value.selectedClinician,
                        title: result.value.optionName,
                        course_of_treatment_id: result.value.selectedCOT,
                        cot_category_id: result.value.selectedCategory
                    },
                },
                success: function(data) {
                    if (data.redirect_url) {
                        window.location.href = data.redirect_url;
                    } else {
                        Swal.fire('Error', 'No redirect URL returned from server', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Failed to create TreatmentPlanOption:", xhr, status, error);
                    Swal.fire('Error', 'Could not import Course of Treatment.', 'error');
                }
            });
        }
    });
}

function startOverlay(notes) {
    const $overlay = $('.tp-details-notes #overlay');
    const $progressFill = $('.tp-details-notes #progressFill');

    $overlay.css('display', 'flex').addClass('show');
    $progressFill.css('width', '0%');

    let progress = 0;
    const interval = setInterval(() => {
        progress += 0.5;
        $progressFill.css('width', `${progress}%`);
        if (progress >= 100) {
            clearInterval(interval);
            $overlay.fadeOut(300);
            // notes.disabled = false;
        }
    }, 100);
}

function updateTreatmentPlanOptionTotals() {
    let subtotal = 0;
    let discounts = 0;
    let treatmentPlanTotal = 0;

    document.querySelectorAll('.override-price').forEach(function(element) {
        let price = parseFloat(element.value) || 0;
        subtotal += price;
    });

    document.querySelectorAll('.discount-amount').forEach(function(element) {
        let discount = parseFloat(element.value) || 0;
        const discountRow = element.closest('tr');
        const treatmentId = discountRow.dataset.chartedTreatmentId;

        let overridePriceField = document.getElementById(`override_price_${treatmentId}`);
        const overridePrice = parseFloat(overridePriceField.value) || 0;

        const discountedType = discountRow.querySelector('select').value;

        if (discountedType === 'Percentage') {
            let discountValue = overridePrice * discount / 100;
            discounts += discountValue;
            // if (type === 'init') {
            //     overridePriceField.value = (overridePrice - discountValue).toFixed(2);
            // }
        } else {
            discounts += discount;
            // if (type === 'init') {
            //     overridePriceField.value = (overridePrice - discount).toFixed(2);
            // }
        }
    });

    treatmentPlanTotal = subtotal - discounts;
    document.getElementById('subtotal').textContent = subtotal.toFixed(2);
    document.getElementById('discounts').textContent = discounts.toFixed(2);
    document.getElementById('treatment_plan_total').textContent = treatmentPlanTotal.toFixed(2);
}

function waitForTinyMCE(id, callback, attempts = 10, delay = 200) {
    const editor = tinymce.get(id);
    if (editor) {
        callback(editor);
    } else if (attempts > 0) {
        setTimeout(() => {
            waitForTinyMCE(id, callback, attempts - 1, delay);
        }, delay);
    } else {
        console.warn("TinyMCE editor not found:", id);
    }
}

window.updateTreatmentPlanOptionTotals = updateTreatmentPlanOptionTotals;
window.toggleDiscountMenu = toggleDiscountMenu;
window.calculateDiscountedPrice = calculateDiscountedPrice;
