# frozen_string_literal: true

class CancellationListAutomationJob
  include Sidekiq::Job

  def perform(practitioner_id)
    practitioner = User.find(practitioner_id)

    practitioner
      .practitioner_bookings
      .not_cancelled
      .cancellation_list
      .on_or_after_date(Date.current)
      .order(start_time: :asc)
      .where.not(booking_type: 'event')
      .find_each do |booking| # TODO: skip bookings if notification was sent recently
        criteria = booking.cancellation_list_criteria || {}

        next if criteria.empty?

        available_slots = Calendar::SlotFinder.new(
          practitioners: practitioner,
          start_date: Date.current.to_s,
          end_date: booking.start_time.to_date.to_s, # try to find slots before the booking
          start_time: Calendar::SlotFinder::SHIFT_START_TIME, # TODO: add shifts
          end_time: Calendar::SlotFinder::SHIFT_END_TIME, # TODO: add shifts
          duration: booking.duration_in_minutes,
          practice: practitioner.practices.first # TODO: fix, should work separately for each practice
        ).run

        matching_slots = filter_slots_by_criteria(available_slots, criteria, booking.start_time)

        offer_slot_to_patient(booking, matching_slots.first) if matching_slots.any?
      end
  end

  private

  def filter_slots_by_criteria(slots, criteria, current_booking_start_time)
    slots.select do |slot|
      next false if slot.end >= current_booking_start_time || overlaps_with_booking?(slot)

      day_name = slot.begin.strftime('%A').downcase
      day_criteria = criteria[day_name]

      next true unless day_criteria

      next false if ActiveModel::Type::Boolean.new.cast(day_criteria['no_times_suitable'])

      from_time = day_criteria['from'] || '08:00'
      to_time = day_criteria['to'] || '20:00'

      slot_date = slot.begin.to_date
      from_datetime = Time.zone.parse("#{slot_date} #{from_time}")
      to_datetime = Time.zone.parse("#{slot_date} #{to_time}")

      slot.begin >= from_datetime && slot.end <= to_datetime
    end
  end

  def overlaps_with_booking?(slot)
    booking.overlaps?(slot.begin, slot.end)
  end

  def offer_slot_to_patient(booking, slot)
    Rails.logger.info "Offering slot #{slot.begin} - #{slot.end} to patient #{booking.patient_id} for booking #{booking.id}"
    # TBD: send notifications (SMS + in-app)
  end
end
