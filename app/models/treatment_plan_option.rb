# frozen_string_literal: true

# == Schema Information
#
# Table name: treatment_plan_options
#
#  id                                 :bigint           not null, primary key
#  patient_id                         :bigint
#  reason                             :text
#  services_provided                  :text
#  outcome                            :text
#  status                             :string           default("New")
#  signed_at                          :datetime
#  oral_health_notes                  :text
#  created_by_id                      :bigint
#  proposed_course_of_treatment_notes :text
#  treatment_coordinator_id           :bigint
#  main_clinician_id                  :bigint
#  sent_at                            :datetime
#  pandadoc_id                        :string
#  treatment_plan_id                  :bigint
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  team_member_ids                    :json
#  treatment_plan_information_ids     :json
#  treatment_plan_form_ids            :json
#  treatment_plan_story_ids           :json
#  import_id                          :string
#  import_source                      :integer
#  import_data                        :jsonb
#  course_of_treatment_id             :bigint
#  title                              :string
#  cot_category_id                    :bigint
#  draft                              :boolean          default(TRUE), not null
#  signature_type                     :string
#  signature_text                     :string
#  charted_treatment_prices           :jsonb
#  viewed_at                          :datetime
#
class TreatmentPlanOption < ApplicationRecord
  include EventLoggable
  include Importable

  belongs_to :patient, optional: true
  belongs_to :treatment_plan
  belongs_to :created_by, class_name: 'User', optional: true
  belongs_to :treatment_coordinator, class_name: 'User', optional: true
  belongs_to :main_clinician, class_name: 'User', optional: true
  belongs_to :course_of_treatment, class_name: 'CourseOfTreatment', optional: true
  belongs_to :cot_category, class_name: 'CotCategory', optional: true
  has_one :practice, through: :treatment_plan
  has_many :signable_documents, dependent: :destroy

  has_one_attached :pdf_file
  has_one_attached :signed_pdf_file
  has_one_attached :signature_image

  accepts_nested_attributes_for :treatment_plan, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :course_of_treatment, reject_if: :all_blank, allow_destroy: true

  after_update :notify_treatment_plan_option_signed

  scope :accepted, -> { where(status: 'Accepted') }
  scope :sent_for_viewing, -> { where(status: 'Sent for Viewing') }

  def self.ransackable_attributes(_auth_object = nil)
    %w[status]
  end

  def total_price
    course_of_treatment&.charted_treatments&.includes(:treatment)
                       &.sum { |ch_t| charted_treatment_prices&.dig(ch_t.id.to_s, 'override_price').to_f || ch_t.price.to_f }
  end

  def pdf_variables
    treatment_plan_option_informations = treatment_plan_information_ids.reject(&:blank?).map do |id|
      TreatmentPlanInformation.find(id)
    end

    treatment_plan_option_forms = treatment_plan_form_ids.reject(&:blank?).map do |id|
      template = DocumentTemplate.find(id)
      template.text = ::Notifications::PlaceholderConverter.convert(self, template.text)
      template
    end

    {
      option: self,
      treatment_plan_option_informations: treatment_plan_option_informations,
      treatment_plan_option_stories: treatment_plan_story_ids.reject(&:blank?).map { |id| TreatmentPlanStory.find(id) },
      treatment_plan_option_team_members: team_member_ids.reject(&:blank?).map { |id| User.find(id) },
      treatment_plan_option_forms: treatment_plan_option_forms,
      site_settings: SiteSetting.first_or_create,
      practice: practice,
      pdf: true
    }
  end

  def notify_treatment_plan_option_signed
    return unless saved_change_to_status? && status == 'Accepted'

    recipients = all_related_users

    description = <<~HTML.strip
      #{patient.full_name} has signed the treatment plan: <strong>#{title} - £#{format('%.2f', total_price)}</strong>. Please review the plan to ensure all details are correct and mark it as complete in the system if appropriate.
    HTML
    recipients.each do |recipient|
      Notification.create(
        recipient: recipient,
        title: "#{patient.full_name} signed treatment plan: #{title} (£#{format('%.2f', total_price)})",
        description: description,
        data: { type: 'treatment_plans',
                sender: patient.full_name,
                color: '#25D366',
                avatar_url: patient.image&.url },
        actions: [
          { text: 'View Treatment Plan', primary: true, action: 'redirect', href: '/admin/treatment_plans' },
          { text: 'Mark Read', action: 'mark_as_read' },
          { text: 'Remind Me Later', action: 'remind_in' }
        ]
      )
    end
  end

  def all_related_users
    (patient.assigned_staff.reject(&:clinician?) + [main_clinician]).uniq
  end
end
