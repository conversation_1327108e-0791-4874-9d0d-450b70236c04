<style>
    /* @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700;800&display=swap'); */

    @page {
        size: A4;
        margin: 1cm;
    }

    .pagebreak {
        page-break-before: always;
    }

    .pdf-section-table {
        width: 100%;
        border-collapse: separate;
        border: none;
        table-layout: fixed;
    }

    /* .docket-table {
      border-collapse: collapse !important;
    } */

    .pdf-card {
        width: 100%;
        height: 100%;
        border: 1px solid black;
        border-radius: 10px;
        padding: 10px;
    }

    .treatment-card {
        border: 1px solid black;
        border-bottom-right-radius: 10px;
        border-top-right-radius: 10px;
        padding: 10px;
        width: 80%;
    }

    .treatment-card-info {
        border: 1px solid black;
        border-bottom-left-radius: 10px;
        border-top-left-radius: 10px;
        padding: 18px;
        width: 20%;
    }
    .treatment-card-info.treatment-private {
        background-color: #23242d;
    }
    .treatment-card-info.treatment-nhs {
        background-color: #8999B0;
    }

    .treatment-card-info > .font-26 {
        color: white;
        padding-bottom: 16px;
    }

    .pdf-field {
        border-radius: 6px;
        border: 1px solid black;
        font-size: 10px;
        width: 100%;
        height: 100%;
        padding: 5px;
        text-align: left;
        min-height: 1em;
    }

    .font-8 {
        font-size: 8px;
        color: #3F4753;
        font-family: Poppins, sans-serif;
        font-weight: 400;
        text-align: center;
    }

    .font-9 {
        font-size: 9px;
        color: #3F4753;
        font-family: Poppins, sans-serif;
        font-weight: 400;
        text-align: center;
    }

    .font-12 {
        font-size: 12px;
        color: black;
        font-family: Poppins, sans-serif;
        font-weight: 400;
        text-align: center;
    }

    .font-14 {
        font-size: 16px;
        color: black;
        font-family: Poppins, sans-serif;
        font-weight: 700;
        text-align: center;
    }

    .pdf-section-header {
        font-size: 14px;
        font-family: Poppins, sans-serif;
        font-weight: 600;
        text-align: center;
        color: #151515;
    }

    .font-26 {
        font-size: 26px;
        color: black;
        font-family: Poppins, sans-serif;
        font-weight: 600;
        text-align: left;
    }

    .font-44 {
        font-size: 32px;
        color: rgba(255, 255, 255, 0.25);
        opacity: 0.3;
        font-family: Poppins, sans-serif;
        font-weight: 700;
        text-align: right;
        font-style: italic;
    }

    .center-stuff {
        text-align: center;
        vertical-align: middle;
    }

    .pdf-time-allowed {
        background: #FBE099;
        border-radius: 4px;
        color: #3F4753;
        font-size: 8px;
        font-weight: 600;
        padding: 6px;
        text-align: center;
    }

    .pdf-label {
        text-align: left !important;
    }

    .separator {
        height: 1px; opacity: 0.10; background: black; width: 100%;
    }
    
     .pdf-numbers-table {
         width: 100%;
         border-collapse: collapse;
     }

    .divider {
        border-left: 1px solid black;
        height: 100%;
        width: 1px;
    }

    .pdf-numbers-cell {
        width: 40px;
        height: 40px;
        text-align: center;
        vertical-align: middle;
        font-size: 20px;
        font-family: 'Poppins', sans-serif;
    }

    .pdf-numbers-red-border {
        border: 1px solid red;
        border-radius: 6px;
    }

    .pdf-horizontal-divider {
        height: 1px;
        background-color: black;
        width: 100%;
    }
</style>

<!-- Info -->
<table class="pdf-section-table">
  <tr>
    <td class="pdf-card">
      <table>
        <tr>
          <td style="width: 42%; padding: 8px; vertical-align: middle; text-align: center;">
            <!-- Logos -->
            <table>
              <!-- Practice -->
              <tr style="height: 50%; margin-bottom: 20px; border-bottom: 1px #E7E8E9 solid;">
                <td style="vertical-align: middle; text-align: center;">
                  <%= image_tag @lab_work.practice&.image&.attached? ? @lab_work.practice.image : @site_settings.logo, style: "width: 50px; height: 50px; border-radius: 100%;" %>
                  <div class="font-9" style="margin-bottom: 8px; margin-top: 8px;">
                    <%= @lab_work.practice&.name || @site_settings.site_name %>
                  </div>
                  <div class="font-9 center-stuff" style="margin-bottom: 4px;">
                    <table>
                      <tr>
                        <td style="text-align: right; align-items: flex-end;">
                          <svg width="14" height="14" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g id="material-symbols-light:stacked-email-outline-rounded">
                              <path id="Vector" d="M5.73783 13.7759C5.37314 13.7759 5.06887 13.6537 4.82504 13.4093C4.5812 13.165 4.45902 12.8607 4.4585 12.4965V4.76194C4.4585 4.39777 4.58068 4.09377 4.82504 3.84994C5.0694 3.60611 5.37366 3.48393 5.73783 3.4834H16.6383C17.0025 3.4834 17.3067 3.60558 17.5511 3.84994C17.7954 4.0943 17.9174 4.39857 17.9168 4.76273V12.4965C17.9168 12.8607 17.7949 13.165 17.5511 13.4093C17.3072 13.6537 17.003 13.7756 16.6383 13.7751L5.73783 13.7759ZM10.8085 9.34252L5.25016 5.20448V12.4957C5.25016 12.6377 5.29582 12.7543 5.38712 12.8456C5.47843 12.937 5.59533 12.9826 5.73783 12.9826H16.6383C16.7803 12.9826 16.8969 12.937 16.9882 12.8456C17.0795 12.7543 17.1252 12.6377 17.1252 12.4957V5.20448L11.5669 9.34094C11.4518 9.42644 11.3254 9.46919 11.1877 9.46919C11.0499 9.46919 10.9235 9.42644 10.8085 9.34094M11.1877 8.66248L16.9732 4.36611C16.9426 4.3355 16.9096 4.31254 16.8742 4.29723C16.8383 4.28193 16.79 4.27427 16.7293 4.27427H5.646C5.59533 4.27427 5.54466 4.28694 5.494 4.31227C5.44333 4.33761 5.40269 4.36558 5.37208 4.39619L11.1877 8.66248ZM3.36283 16.1493C2.99813 16.1493 2.69387 16.0274 2.45004 15.7835C2.2062 15.5397 2.08402 15.2357 2.0835 14.8715V7.74652C2.0835 7.63358 2.12123 7.53937 2.1967 7.4639C2.27218 7.38843 2.36638 7.35069 2.47933 7.35069C2.59227 7.35069 2.68648 7.38843 2.76195 7.4639C2.83743 7.53937 2.87516 7.63358 2.87516 7.74652V14.8715C2.87516 15.0135 2.92082 15.1301 3.01212 15.2214C3.10343 15.3127 3.22033 15.3584 3.36283 15.3584H14.6591C14.7715 15.3584 14.8655 15.3961 14.941 15.4716C15.0164 15.5471 15.0544 15.6413 15.055 15.7542C15.0555 15.8672 15.0175 15.9614 14.941 16.0369C14.8644 16.1123 14.7705 16.1501 14.6591 16.1501L3.36283 16.1493ZM17.1252 5.19182C17.1252 5.04562 17.1075 4.90048 17.0721 4.7564C17.0362 4.61231 17.0032 4.48248 16.9732 4.3669C16.9426 4.33629 16.9096 4.31254 16.8742 4.29723C16.8383 4.28193 16.79 4.27427 16.7293 4.27427H5.646C5.59533 4.27427 5.54466 4.28694 5.494 4.31227C5.44333 4.33761 5.40269 4.36558 5.37208 4.39619C5.34147 4.5123 5.3135 4.64055 5.28816 4.77936C5.26283 4.91816 5.25016 5.05565 5.25016 5.19182V4.27507H17.1252V5.19182Z" fill="#151515"/>
                            </g>
                          </svg>
                        </td>
                        <td class="font-9" style="text-align: center; white-space: nowrap; overflow: hidden;">
                          <%= @lab_work.practice&.email %>
                        </td>
                      </tr>
                    </table>
                  </div>
                  <div class="font-9 center-stuff">
                    <table>
                      <tr>
                        <td style="text-align: right;">
                          <svg width="14" height="14" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g id="material-symbols-light:phone-enabled-outline">
                              <path id="Vector" d="M4.51359 16.3332C4.27134 16.3332 4.06946 16.254 3.90796 16.0957C3.74646 15.9373 3.66598 15.7394 3.66651 15.5019V13.575C3.66651 13.3628 3.73353 13.1747 3.86759 13.0105C4.00165 12.8464 4.17634 12.7382 4.39167 12.686L6.05655 12.344C6.25183 12.3176 6.42969 12.3265 6.59013 12.3709C6.75058 12.4152 6.89571 12.5068 7.02555 12.6456L8.71813 14.369C9.35146 14.0181 9.93308 13.6449 10.463 13.2496C10.9929 12.8538 11.4876 12.4263 11.9473 11.9671C12.3928 11.5143 12.8121 11.0348 13.2053 10.5287C13.5985 10.0225 13.9574 9.4747 14.282 8.88517L12.5458 7.26542C12.4197 7.15986 12.3318 7.02449 12.2822 6.8593C12.2326 6.6941 12.226 6.50067 12.2624 6.279L12.6551 4.39088C12.7121 4.17871 12.8208 4.00507 12.9813 3.86996C13.1412 3.73432 13.3288 3.6665 13.5441 3.6665H15.5019C15.7394 3.6665 15.9373 3.74725 16.0957 3.90875C16.254 4.07025 16.3332 4.2716 16.3332 4.5128C16.3332 5.84755 15.9991 7.21317 15.3309 8.60967C14.6628 10.0062 13.727 11.3085 12.5237 12.5165C11.3203 13.7246 10.0183 14.6628 8.61759 15.3309C7.21687 15.9991 5.8486 16.3332 4.5128 16.3332M14.6493 8.14259C14.9142 7.54673 15.1238 6.95456 15.2779 6.36609C15.432 5.77761 15.517 5.21289 15.5328 4.67192C15.5328 4.6107 15.5125 4.55977 15.4718 4.51913C15.4312 4.47849 15.3803 4.45817 15.319 4.45817H13.6993C13.618 4.45817 13.552 4.47849 13.5014 4.51913C13.4507 4.55977 13.4154 4.62046 13.3953 4.70121L13.039 6.37955C13.0185 6.44024 13.0158 6.50621 13.0311 6.57746C13.0464 6.64871 13.0794 6.70466 13.1301 6.7453L14.6493 8.14259ZM7.95972 14.7316L6.47138 13.2116C6.42019 13.1615 6.37163 13.1288 6.32572 13.1135C6.28033 13.0976 6.22728 13.1003 6.16659 13.1214L4.70201 13.4254C4.62073 13.4454 4.55977 13.4808 4.51913 13.5315C4.47849 13.5827 4.45817 13.6486 4.45817 13.7294V15.319C4.45817 15.3797 4.47849 15.4304 4.51913 15.471C4.55977 15.5117 4.61044 15.532 4.67113 15.532C5.1208 15.523 5.64752 15.4536 6.2513 15.3238C6.85508 15.194 7.42455 14.9966 7.95972 14.7316Z" fill="#151515"/>
                            </g>
                          </svg>
                        </td>
                        <td class="font-9" style="text-align: center; white-space: nowrap; overflow: hidden;">
                          <%= @lab_work.practice&.phone %>
                        </td>
                      </tr>
                    </table>
                  </div>
                </td>
              </tr>

              <!-- Spacer -->
              <tr style="height: 12px; width: 100%;"><td></td></tr>

              <!-- Lab -->
              <tr style="height: 50%;">
                <td style="vertical-align: middle; text-align: center;">
                  <%= image_tag @lab_work.lab.image.attached? ? @lab_work.lab.image : "default-avatar.webp", style: "width: 50px; height: 50px; border-radius: 100%;" %>
                  <div class="font-9" style="margin-bottom: 8px; margin-top: 8px;"><%= @lab_work.lab.name %></div>
                  <div class="font-9 center-stuff">
                    <table>
                      <tr>
                        <td style="text-align: right; ">
                          <svg width="14" height="14" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g id="material-symbols-light:stacked-email-outline-rounded">
                              <path id="Vector" d="M5.73783 13.7759C5.37314 13.7759 5.06887 13.6537 4.82504 13.4093C4.5812 13.165 4.45902 12.8607 4.4585 12.4965V4.76194C4.4585 4.39777 4.58068 4.09377 4.82504 3.84994C5.0694 3.60611 5.37366 3.48393 5.73783 3.4834H16.6383C17.0025 3.4834 17.3067 3.60558 17.5511 3.84994C17.7954 4.0943 17.9174 4.39857 17.9168 4.76273V12.4965C17.9168 12.8607 17.7949 13.165 17.5511 13.4093C17.3072 13.6537 17.003 13.7756 16.6383 13.7751L5.73783 13.7759ZM10.8085 9.34252L5.25016 5.20448V12.4957C5.25016 12.6377 5.29582 12.7543 5.38712 12.8456C5.47843 12.937 5.59533 12.9826 5.73783 12.9826H16.6383C16.7803 12.9826 16.8969 12.937 16.9882 12.8456C17.0795 12.7543 17.1252 12.6377 17.1252 12.4957V5.20448L11.5669 9.34094C11.4518 9.42644 11.3254 9.46919 11.1877 9.46919C11.0499 9.46919 10.9235 9.42644 10.8085 9.34094M11.1877 8.66248L16.9732 4.36611C16.9426 4.3355 16.9096 4.31254 16.8742 4.29723C16.8383 4.28193 16.79 4.27427 16.7293 4.27427H5.646C5.59533 4.27427 5.54466 4.28694 5.494 4.31227C5.44333 4.33761 5.40269 4.36558 5.37208 4.39619L11.1877 8.66248ZM3.36283 16.1493C2.99813 16.1493 2.69387 16.0274 2.45004 15.7835C2.2062 15.5397 2.08402 15.2357 2.0835 14.8715V7.74652C2.0835 7.63358 2.12123 7.53937 2.1967 7.4639C2.27218 7.38843 2.36638 7.35069 2.47933 7.35069C2.59227 7.35069 2.68648 7.38843 2.76195 7.4639C2.83743 7.53937 2.87516 7.63358 2.87516 7.74652V14.8715C2.87516 15.0135 2.92082 15.1301 3.01212 15.2214C3.10343 15.3127 3.22033 15.3584 3.36283 15.3584H14.6591C14.7715 15.3584 14.8655 15.3961 14.941 15.4716C15.0164 15.5471 15.0544 15.6413 15.055 15.7542C15.0555 15.8672 15.0175 15.9614 14.941 16.0369C14.8644 16.1123 14.7705 16.1501 14.6591 16.1501L3.36283 16.1493ZM17.1252 5.19182C17.1252 5.04562 17.1075 4.90048 17.0721 4.7564C17.0362 4.61231 17.0032 4.48248 16.9732 4.3669C16.9426 4.33629 16.9096 4.31254 16.8742 4.29723C16.8383 4.28193 16.79 4.27427 16.7293 4.27427H5.646C5.59533 4.27427 5.54466 4.28694 5.494 4.31227C5.44333 4.33761 5.40269 4.36558 5.37208 4.39619C5.34147 4.5123 5.3135 4.64055 5.28816 4.77936C5.26283 4.91816 5.25016 5.05565 5.25016 5.19182V4.27507H17.1252V5.19182Z" fill="#151515"/>
                            </g>
                          </svg>
                        </td>
                        <td class="font-9" style="text-align: center; white-space: nowrap; overflow: hidden;">
                          <%= @lab_work.lab.email_address %>
                        </td>
                      </tr>
                    </table>
                  </div>
                  <div class="font-9 center-stuff">
                    <table>
                      <tr>
                        <td style="text-align: right; ">
                          <svg width="14" height="14" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g id="material-symbols-light:phone-enabled-outline">
                              <path id="Vector" d="M4.51359 16.3332C4.27134 16.3332 4.06946 16.254 3.90796 16.0957C3.74646 15.9373 3.66598 15.7394 3.66651 15.5019V13.575C3.66651 13.3628 3.73353 13.1747 3.86759 13.0105C4.00165 12.8464 4.17634 12.7382 4.39167 12.686L6.05655 12.344C6.25183 12.3176 6.42969 12.3265 6.59013 12.3709C6.75058 12.4152 6.89571 12.5068 7.02555 12.6456L8.71813 14.369C9.35146 14.0181 9.93308 13.6449 10.463 13.2496C10.9929 12.8538 11.4876 12.4263 11.9473 11.9671C12.3928 11.5143 12.8121 11.0348 13.2053 10.5287C13.5985 10.0225 13.9574 9.4747 14.282 8.88517L12.5458 7.26542C12.4197 7.15986 12.3318 7.02449 12.2822 6.8593C12.2326 6.6941 12.226 6.50067 12.2624 6.279L12.6551 4.39088C12.7121 4.17871 12.8208 4.00507 12.9813 3.86996C13.1412 3.73432 13.3288 3.6665 13.5441 3.6665H15.5019C15.7394 3.6665 15.9373 3.74725 16.0957 3.90875C16.254 4.07025 16.3332 4.2716 16.3332 4.5128C16.3332 5.84755 15.9991 7.21317 15.3309 8.60967C14.6628 10.0062 13.727 11.3085 12.5237 12.5165C11.3203 13.7246 10.0183 14.6628 8.61759 15.3309C7.21687 15.9991 5.8486 16.3332 4.5128 16.3332M14.6493 8.14259C14.9142 7.54673 15.1238 6.95456 15.2779 6.36609C15.432 5.77761 15.517 5.21289 15.5328 4.67192C15.5328 4.6107 15.5125 4.55977 15.4718 4.51913C15.4312 4.47849 15.3803 4.45817 15.319 4.45817H13.6993C13.618 4.45817 13.552 4.47849 13.5014 4.51913C13.4507 4.55977 13.4154 4.62046 13.3953 4.70121L13.039 6.37955C13.0185 6.44024 13.0158 6.50621 13.0311 6.57746C13.0464 6.64871 13.0794 6.70466 13.1301 6.7453L14.6493 8.14259ZM7.95972 14.7316L6.47138 13.2116C6.42019 13.1615 6.37163 13.1288 6.32572 13.1135C6.28033 13.0976 6.22728 13.1003 6.16659 13.1214L4.70201 13.4254C4.62073 13.4454 4.55977 13.4808 4.51913 13.5315C4.47849 13.5827 4.45817 13.6486 4.45817 13.7294V15.319C4.45817 15.3797 4.47849 15.4304 4.51913 15.471C4.55977 15.5117 4.61044 15.532 4.67113 15.532C5.1208 15.523 5.64752 15.4536 6.2513 15.3238C6.85508 15.194 7.42455 14.9966 7.95972 14.7316Z" fill="#151515"/>
                            </g>
                          </svg>
                        </td>
                        <td class="font-9" style="text-align: center; white-space: nowrap; overflow: hidden;">
                          <%= @lab_work.lab.contact_number %>
                        </td>
                      </tr>
                    </table>
                  </div>
                </td>
              </tr>
            </table>
          </td>

          <td style="width: 56%; padding: 8px; vertical-align: top !important; vertical-align: middle; text-align: center;">
            <table class="center-stuff">
              <tr class="center-stuff">
                <td class="center-stuff" style="width: 30%;">
                  <div class="font-9 pdf-label">Dentist Name</div>
                </td>
                <td class="center-stuff" style="width: 70%;">
                  <div class="pdf-field font-9 center-stuff"><%= @lab_work.practitioner.full_name %></div>
                </td>
              </tr>

              <tr>
                <td class="width: 30%">
                  <div class="font-9 pdf-label">Email</div>
                </td>
                <td class="width: 70%">
                  <div class="pdf-field font-9"><%= @lab_work.practitioner.email %></div>
                </td>
              </tr>

              <tr>
                <td class="width: 30%">
                  <div class="font-9 pdf-label">Phone</div>
                </td>
                <td class="width: 70%">
                  <div class="pdf-field font-9"><%= @lab_work.practitioner.mobile_phone %></div>
                </td>
              </tr>

              <tr>
                <td class="width: 30%">
                  <div class="font-9 pdf-label">Address</div>
                </td>
                <td class="width: 70%">
                  <div class="pdf-field font-9"><%= @lab_work.practice.full_address %></div>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </td>

    <td style="width: 10px"></td>

    <td class="pdf-card">
      <table>
        <tr>
          <td style="width: 32%">
            <div class="font-12" style="margin-bottom: 18px; font-weight: 800;">Patient Records & Clinician Communication</div>

            <div class="center-stuff">
              <%= RQRCode::QRCode.new(@lab_work.code_key).as_svg(viewbox: true, svg_attributes: { style: "width: 100px; height: 100px;" }).html_safe %>
            </div>

            <div class="font-9" style="margin-top: 18px;">Scan the QR code to get your unique pin to access the patient's records.</div>
          </td>

          <td style="width: 6%"></td>

          <td style="width: 62%">
            <!-- pdf-fields -->
            <table style="width: 100%">
              <tr style="width: 100%">
                <td style="width: 48%">
                  <div class="font-9 pdf-label" style="margin-bottom: 4px;">Patient ID</div>
                  <div class="pdf-field font-9" style="padding: 4px;"><%= @lab_work.patient.id %></div>
                </td>
                <td style="width: 4%"></td>
                <td style="width: 48%">
                  <div class="font-9 pdf-label" style="margin-bottom: 4px;">Job Number</div>
                  <div class="pdf-field font-9" style="padding: 4px;"><%= parse_docket_info(@lab_docket.docket_info)['job_number_for_lab_use'] %></div>
                </td>
              </tr>
              <tr style="width: 100%">
                <td style="width: 48%">
                  <div class="font-9 pdf-label" style="margin-bottom: 4px;">Date Sent</div>
                  <div class="pdf-field font-9" style="padding: 4px;"><%= (@lab_docket.date_in.strftime("%Y-%m-%d")) %></div>
                </td>
                <td style="width: 4%"></td>
                <td style="width: 48%">
                  <div class="font-9 pdf-label" style="margin-bottom: 4px;">Date Due</div>
                  <div class="pdf-field font-9" style="padding: 4px;"><%= (@lab_docket.date_return.strftime("%Y-%m-%d")) %></div>
                </td>
              </tr>
            </table>


            <!-- Time Allowed -->
            <table width="100%" style="margin-top: 18px;">
              <tr>
                <td><div class="font-8 pdf-label" style="text-align: center; vertical-align: center;">Time Allowed</div></td>
                <td><div class="pdf-time-allowed"><%= @lab_docket.total_days %> days</div></td>
              </tr>
            </table>

            <!-- Instructions -->
            <div style="margin-top: 18px;">
              <div class="font-9 pdf-label" style="margin-bottom: 4px; margin-top: 4px;">Instructions (lab use only)</div>
              <div class="pdf-field font-9" style="padding: 4px;"><%= @lab_docket.instructions %></div>
            </div>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>

<% @lab_docket_items.each_with_index do |lab_docket_item, idx| %>
  <!-- Treatment -->
  <% [lab_docket_item.lab_docket_treatments.last].each do |lab_docket_treatment| %>
    <table class="pdf-section-table docket-table" style="margin-top: 10px;">
      <tr>
        <td class="treatment-card-info treatment-<%= lab_docket_treatment&.lab_item&.treatment_type&.downcase %>">
          <div class="font-26"><%= lab_docket_treatment.lab_item&.name %></div>
          <div class="font-44" style="padding-right: 20px;"><%= lab_docket_treatment&.lab_item&.treatment_type&.upcase %></div>
        </td>
        <td class="treatment-card" style="padding: 14px !important;">
          <table style="width: 100%;">
            <tr>
              <td style="width: 70%">
                <table>
                  <tr>
                    <% if !lab_docket_treatment.materials.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Materials</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.materials || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                    <% if !lab_docket_treatment.margin.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Margin</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.margin || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                    <% if !lab_docket_treatment.treatment_type.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Type</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.treatment_type || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                    <% if !lab_docket_treatment.num_wings.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Number of wings</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.num_wings || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                  </tr>
                  <tr>
                    <% if !lab_docket_treatment.post_core_materials.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Post & core materials</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.post_core_materials || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                    <% if !lab_docket_treatment.post_core.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Post & core</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.post_core || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                    <% if !lab_docket_treatment.stage.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Stage</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.stage || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                    <% if !lab_docket_treatment.implant_system.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Implant system</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.implant_system || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                    <% if !lab_docket_treatment.retention.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Retention</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.retention || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                    <% if !lab_docket_treatment.abutment_material.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Abutment material</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.abutment_material || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                    <% if !lab_docket_treatment.arch.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Arch</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.arch || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                    <% if !lab_docket_treatment.units.blank?%>
                      <td>
                        <div class="font-9 pdf-label" style="margin-bottom: 4px;">Units</div>
                        <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.units || '-' %></div>
                      </td>
                      <td style="width: 12px;"></td>
                    <% end %>
                  </tr>
                </table>
              </td>

<!--              <td style="width: 15%">-->
<!--                <div class="font-12" style="text-align: left !important; margin-right: 20px">-->
<!--                  <div class="font-9 pdf-label" style="margin-bottom: 4px;">Notes</div>-->
<!--                  <div class="pdf-field font-9" style="padding: 4px;"><%#= lab_docket_treatment&.arch.presence %></div>-->

<!--                </div>-->
<!--              </td>-->

              <td style="width: 15%">
                <div class="font-12" style="text-align: left !important;">
                  <div class="font-9 pdf-label" style="margin-bottom: 4px;">Notes</div>
                  <div class="pdf-field font-9" style="padding: 4px;"><%= lab_docket_treatment&.further_information.presence %></div>

                </div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  <% end %>

  <!-- Tooth and numbers-->
  <table class="pdf-section-table" style="margin-top: 10px;">
    <tr>
      <td class="pdf-card" style="padding: 0 !important; width: 500px; border: none !important">
        <table>
          <tr>
            <td class="pdf-card" style="padding: 14px !important; width: 500px; height: 200px;">
              <table>
                <tr>
                  <td style="width: 200px;">
                    <table>
                      <% if lab_docket_item.info && lab_docket_item.info['stump_shade'].present? %>
                        <tr>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: left; width: 130px;">Stump Shade</div></td>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: right; width: 130px;"><%= lab_docket_item.info['stump_shade'] %></div></td>
                        </tr>
                      <% end %>

                      <% if lab_docket_item.info && lab_docket_item.info['vita_classic'].present? %>
                        <tr>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: left; width: 130px;">Vita Classic</div></td>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: right; width: 130px;"><%= lab_docket_item.info['vita_classic'] %></div></td>
                        </tr>
                      <% end %>

                      <% if lab_docket_item.info && lab_docket_item.info['three_dimensional_master'].present? %>
                        <tr>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: left; width: 130px;">3D Master</div></td>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: right; width: 130px;"><%= lab_docket_item.info['three_dimensional_master'] %></div></td>
                        </tr>
                      <% end %>

                      <% if lab_docket_item.info && lab_docket_item.info['translucency'].present? %>
                        <tr>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: left; width: 130px;">Translucency</div></td>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: right; width: 130px;"><%= lab_docket_item.info['translucency'] %></div></td>
                        </tr>
                      <% end %>

                      <% if lab_docket_item.info && lab_docket_item.info['surface_texture'].present? %>
                        <tr>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: left; width: 130px;">Surface Texture</div></td>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: right; width: 130px;"><%= lab_docket_item.info['surface_texture'] %></div></td>
                        </tr>
                      <% end %>

                      <% if lab_docket_item.info && lab_docket_item.info['physical_impressions_taken'].present? %>
                        <tr>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: left; width: 130px;">Physical Impressions</div></td>
                          <td>
                            <div class="font-12" style="padding-bottom: 7px;text-align: right; width: 130px;">
                              <span style="vertical-align: middle; padding-right: 4px;"><%= lab_docket_item.info['physical_impressions_taken'] == '1' ? 'Yes' : 'No' %></span>
                              <% if lab_docket_item.info['physical_impressions_taken'] == '1' %>
                                <svg width="25" height="26" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg" style="vertical-align: middle;">
                                  <path d="M10.8223 16.2032L7.91033 13.2912C7.80067 13.1823 7.6665 13.124 7.50783 13.1162C7.34917 13.1085 7.208 13.1668 7.08433 13.2912C6.96067 13.4157 6.89844 13.5533 6.89767 13.7042C6.89689 13.8551 6.95911 13.9928 7.08433 14.1172L10.162 17.1949C10.3502 17.3839 10.5699 17.4784 10.8212 17.4784C11.0724 17.4784 11.2925 17.3839 11.4815 17.1949L17.8713 10.8062C17.9802 10.6966 18.0386 10.5624 18.0463 10.4037C18.0541 10.2451 17.9958 10.1039 17.8713 9.98023C17.7469 9.85657 17.6092 9.79434 17.4583 9.79357C17.3074 9.79279 17.1698 9.85501 17.0453 9.98023L10.8223 16.2032ZM12.5035 23.7959C11.0522 23.7959 9.68717 23.5206 8.4085 22.9699C7.13061 22.4185 6.01878 21.6702 5.073 20.7252C4.12722 19.7802 3.37861 18.6696 2.82717 17.3932C2.27572 16.1169 2 14.7523 2 13.2994C2 11.8465 2.27572 10.4815 2.82717 9.2044C3.37783 7.92651 4.12489 6.81468 5.06833 5.8689C6.01178 4.92312 7.12283 4.17451 8.4015 3.62307C9.68017 3.07162 11.0452 2.7959 12.4965 2.7959C13.9478 2.7959 15.3128 3.07162 16.5915 3.62307C17.8694 4.17373 18.9812 4.92118 19.927 5.8654C20.8728 6.80962 21.6214 7.92068 22.1728 9.19857C22.7243 10.4765 23 11.8411 23 13.2924C23 14.7437 22.7247 16.1087 22.174 17.3874C21.6233 18.6661 20.8751 19.7779 19.9293 20.7229C18.9836 21.6679 17.8729 22.4165 16.5973 22.9687C15.3218 23.521 13.9572 23.7967 12.5035 23.7959ZM12.5 22.6292C15.1056 22.6292 17.3125 21.7251 19.1208 19.9167C20.9292 18.1084 21.8333 15.9015 21.8333 13.2959C21.8333 10.6903 20.9292 8.4834 19.1208 6.67507C17.3125 4.86673 15.1056 3.96257 12.5 3.96257C9.89444 3.96257 7.6875 4.86673 5.87917 6.67507C4.07083 8.4834 3.16667 10.6903 3.16667 13.2959C3.16667 15.9015 4.07083 18.1084 5.87917 19.9167C7.6875 21.7251 9.89444 22.6292 12.5 22.6292Z" fill="black"/>
                                  </svg>
                              <% end %>
                            </div>
                          </td>
                        </tr>
                      <% end %>

                      <% if lab_docket_item.info && lab_docket_item.info['intraoral_scan_taken'].present? %>
                        <tr>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: left; width: 130px;">Intraoral scan taken</div></td>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: right; width: 130px;"><span style="vertical-align: middle; padding-right: 4px;"><%= lab_docket_item.info['intraoral_scan_taken'] == '1' ? 'Yes' : 'No' %></span><% if lab_docket_item.info['intraoral_scan_taken'] == '1' %> <svg width="25" height="26" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg" style="vertical-align: middle;">
                            <path d="M10.8223 16.2032L7.91033 13.2912C7.80067 13.1823 7.6665 13.124 7.50783 13.1162C7.34917 13.1085 7.208 13.1668 7.08433 13.2912C6.96067 13.4157 6.89844 13.5533 6.89767 13.7042C6.89689 13.8551 6.95911 13.9928 7.08433 14.1172L10.162 17.1949C10.3502 17.3839 10.5699 17.4784 10.8212 17.4784C11.0724 17.4784 11.2925 17.3839 11.4815 17.1949L17.8713 10.8062C17.9802 10.6966 18.0386 10.5624 18.0463 10.4037C18.0541 10.2451 17.9958 10.1039 17.8713 9.98023C17.7469 9.85657 17.6092 9.79434 17.4583 9.79357C17.3074 9.79279 17.1698 9.85501 17.0453 9.98023L10.8223 16.2032ZM12.5035 23.7959C11.0522 23.7959 9.68717 23.5206 8.4085 22.9699C7.13061 22.4185 6.01878 21.6702 5.073 20.7252C4.12722 19.7802 3.37861 18.6696 2.82717 17.3932C2.27572 16.1169 2 14.7523 2 13.2994C2 11.8465 2.27572 10.4815 2.82717 9.2044C3.37783 7.92651 4.12489 6.81468 5.06833 5.8689C6.01178 4.92312 7.12283 4.17451 8.4015 3.62307C9.68017 3.07162 11.0452 2.7959 12.4965 2.7959C13.9478 2.7959 15.3128 3.07162 16.5915 3.62307C17.8694 4.17373 18.9812 4.92118 19.927 5.8654C20.8728 6.80962 21.6214 7.92068 22.1728 9.19857C22.7243 10.4765 23 11.8411 23 13.2924C23 14.7437 22.7247 16.1087 22.174 17.3874C21.6233 18.6661 20.8751 19.7779 19.9293 20.7229C18.9836 21.6679 17.8729 22.4165 16.5973 22.9687C15.3218 23.521 13.9572 23.7967 12.5035 23.7959ZM12.5 22.6292C15.1056 22.6292 17.3125 21.7251 19.1208 19.9167C20.9292 18.1084 21.8333 15.9015 21.8333 13.2959C21.8333 10.6903 20.9292 8.4834 19.1208 6.67507C17.3125 4.86673 15.1056 3.96257 12.5 3.96257C9.89444 3.96257 7.6875 4.86673 5.87917 6.67507C4.07083 8.4834 3.16667 10.6903 3.16667 13.2959C3.16667 15.9015 4.07083 18.1084 5.87917 19.9167C7.6875 21.7251 9.89444 22.6292 12.5 22.6292Z" fill="black"/>
                          </svg>
                            <% end %></div></td>
                        </tr>
                      <% end %>

                      <% if lab_docket_item.info && lab_docket_item.info['clinical_photos_taken'].present? %>
                        <tr>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: left; width: 130px;">Clinical photos taken</div></td>
                          <td><div class="font-12" style="padding-bottom: 7px;text-align: right; width: 130px;"><span style="vertical-align: middle; padding-right: 4px;"><%= lab_docket_item.info['clinical_photos_taken'] == '1' ? 'Yes' : 'No' %></span><% if lab_docket_item.info['clinical_photos_taken'] == '1' %> <svg width="25" height="26" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg" style="vertical-align: middle;">
                            <path d="M10.8223 16.2032L7.91033 13.2912C7.80067 13.1823 7.6665 13.124 7.50783 13.1162C7.34917 13.1085 7.208 13.1668 7.08433 13.2912C6.96067 13.4157 6.89844 13.5533 6.89767 13.7042C6.89689 13.8551 6.95911 13.9928 7.08433 14.1172L10.162 17.1949C10.3502 17.3839 10.5699 17.4784 10.8212 17.4784C11.0724 17.4784 11.2925 17.3839 11.4815 17.1949L17.8713 10.8062C17.9802 10.6966 18.0386 10.5624 18.0463 10.4037C18.0541 10.2451 17.9958 10.1039 17.8713 9.98023C17.7469 9.85657 17.6092 9.79434 17.4583 9.79357C17.3074 9.79279 17.1698 9.85501 17.0453 9.98023L10.8223 16.2032ZM12.5035 23.7959C11.0522 23.7959 9.68717 23.5206 8.4085 22.9699C7.13061 22.4185 6.01878 21.6702 5.073 20.7252C4.12722 19.7802 3.37861 18.6696 2.82717 17.3932C2.27572 16.1169 2 14.7523 2 13.2994C2 11.8465 2.27572 10.4815 2.82717 9.2044C3.37783 7.92651 4.12489 6.81468 5.06833 5.8689C6.01178 4.92312 7.12283 4.17451 8.4015 3.62307C9.68017 3.07162 11.0452 2.7959 12.4965 2.7959C13.9478 2.7959 15.3128 3.07162 16.5915 3.62307C17.8694 4.17373 18.9812 4.92118 19.927 5.8654C20.8728 6.80962 21.6214 7.92068 22.1728 9.19857C22.7243 10.4765 23 11.8411 23 13.2924C23 14.7437 22.7247 16.1087 22.174 17.3874C21.6233 18.6661 20.8751 19.7779 19.9293 20.7229C18.9836 21.6679 17.8729 22.4165 16.5973 22.9687C15.3218 23.521 13.9572 23.7967 12.5035 23.7959ZM12.5 22.6292C15.1056 22.6292 17.3125 21.7251 19.1208 19.9167C20.9292 18.1084 21.8333 15.9015 21.8333 13.2959C21.8333 10.6903 20.9292 8.4834 19.1208 6.67507C17.3125 4.86673 15.1056 3.96257 12.5 3.96257C9.89444 3.96257 7.6875 4.86673 5.87917 6.67507C4.07083 8.4834 3.16667 10.6903 3.16667 13.2959C3.16667 15.9015 4.07083 18.1084 5.87917 19.9167C7.6875 21.7251 9.89444 22.6292 12.5 22.6292Z" fill="black"/>
                          </svg>
                            <% end %></div></td>
                        </tr>
                      <% end %>
                    </table>
                  </td>

                  <td style="width: 24px"></td>

                  <td>
                    <div style="border-radius: 10px; border: 1px #E7E8E9 solid; padding-top: 16px; padding-bottom: 16px;">
                      <img id="imagedrawshade" width="190px" height="120px" src="<% if lab_docket_item.info["drawnimagedata"].blank? %><%= request.base_url %>/images/lds.svg<% else %><%= lab_docket_item.info["drawnimagedata"] %><% end %>">
                    </div>
                  </td>
                </tr>
                <% if lab_docket_item.info && lab_docket_item.info['additional_instructions'].present? %>
                  <tr>
                    <td  style="padding-left: 5px !important;">
                      <div class="font-12" style="padding-bottom: 7px;text-align: left; width: 130px;">
                        Further Instructions:
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding-left: 5px !important;">
                      <div class="font-12" style="padding-bottom: 7px;text-align: left; width: 130px;">
                        <%= lab_docket_item.info['additional_instructions'] %>
                      </div>
                    </td>
                  </tr>
                <% end %>
              </table>
            </td>
          </tr>
          <tr>
            <td class="pdf-card" style="padding: 14px !important;">
              <div class="pdf-section-header" style="vertical-align: top; text-align: left; margin-bottom: 12px; font-weight: 600">Numbers</div>

              <table class="pdf-numbers-table">
                <!-- Top Row -->
                <tr>
                  <% ['topl8', 'topl7', 'topl6', 'topl5', 'topl4', 'topl3', 'topl2', 'topl1'].each_with_index do |key, index| %>
                    <td class="pdf-numbers-cell <%= 'pdf-numbers-red-border' if lab_docket_item.info[key] == '1' %>">
                      <%= 8 - index %>
                    </td>
                  <% end %>

                  <!-- Vertical Divider -->
                  <td class="divider"></td>

                  <% ['topr1', 'topr2', 'topr3', 'topr4', 'topr5', 'topr6', 'topr7', 'topr8'].each_with_index do |key, index| %>
                    <td class="pdf-numbers-cell <%= 'pdf-numbers-red-border' if lab_docket_item.info[key] == '1' %>">
                      <%= index + 1 %>
                    </td>
                  <% end %>
                </tr>

                <!-- Horizontal Divider -->
                <tr>
                  <td colspan="8" style="border: none;">
                    <div class="pdf-horizontal-divider"></div>
                  </td>
                  <td class="divider"></td>
                  <td colspan="8" style="border: none;">
                    <div class="pdf-horizontal-divider"></div>
                  </td>
                </tr>

                <!-- Bottom Row -->
                <tr>
                  <% ['bottoml8', 'bottoml7', 'bottoml6', 'bottoml6', 'bottoml4', 'bottoml3', 'bottoml2', 'bottoml1'].each_with_index do |key, index| %>
                    <td class="pdf-numbers-cell <%= 'pdf-numbers-red-border' if lab_docket_item.info[key] == '1' %>">
                      <%= 8 - index %>
                    </td>
                  <% end %>

                  <!-- Vertical Divider -->
                  <td class="divider"></td>

                  <% ['bottomr1', 'bottomr2', 'bottomr3', 'bottomr4', 'bottomr5', 'bottomr6', 'bottomr7', 'bottomr8'].each_with_index do |key, index| %>
                    <td class="pdf-numbers-cell <%= 'pdf-numbers-red-border' if lab_docket_item.info[key] == '1' %>">
                      <%= index + 1 %>
                    </td>
                  <% end %>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>

      <td style="width: 10px;"></td>

      <td class="pdf-card" style="padding: 14px !important;">
        <table>
          <tr>
            <td>
              <img src="<% if lab_docket_item.info["drawnimagedata_shade"].blank? %><%= request.base_url %>/images/ld.svg<% else %><%= lab_docket_item.info["drawnimagedata_shade"] %><% end %>" width="240" style="padding-left: 45px;">
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>

  <!-- Prescription Details -->
  <% if lab_docket_item.info && lab_docket_item.info['prescription_details'].present? %>
    <table class="pdf-section-table" style="margin-top: 10px;">
      <tr>
        <td class="pdf-card" style="padding: 14px !important;">
          <table width="100%">
            <tr>
              <td><div style="color: black; font-size: 16px; font-family: Poppins, sans-serif; font-weight: 700">Prescription Details</div></td>
            </tr>
          </table>

          <table width="100%">
            <tr>
              <td><div class="font-12" style="text-align: left;"><%= lab_docket_item.info['prescription_details'] %></div></td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  <% end %>

  <!-- Disinfectant -->
  <table class="pdf-section-table" style="margin-top: 10px;">
    <tr>
      <td class="pdf-card" style="padding: 14px !important;">
        <table>
          <tr>
            <td style="width: 100%;"><div style="color: black; font-size: 16px; font-family: Poppins, sans-serif; font-weight: 700">Disinfectant</div></td>
          </tr>
        </table>

        <table>
          <tr>
            <td style="width: 260px;">
              <table>
                <tr>
                  <td><div class="font-12" style="text-align: left; width: 130px;">Disinfection</div></td>
                  <td><div class="font-12" style="text-align: right; width: 130px;"><%= @lab_docket.disinfected_with.presence || '-' %></div></td>
                </tr>

                <tr class="separator"><td class="separator"></td><td class="separator"></td></tr>

                <tr>
                  <td><div class="font-12" style="text-align: left; width: 130px;">Made available to Lab</div></td>
                  <td><div class="font-12" style="text-align: right; width: 130px;"><%= @lab_docket.made_available.presence || '-' %></div></td>
                </tr>

                <tr class="separator"><td class="separator"></td><td class="separator"></td></tr>

                <tr>
                  <td><div class="font-12" style="text-align: left; width: 130px;">Disinfected by</div></td>
                  <td><div class="font-12" style="text-align: right; width: 130px;"><%= @lab_docket.disinfected_by&.full_name || '-' %></div></td>
                </tr>
              </table>
            </td>

            <td style="width: 24px"></td>

            <td>
              <div class="font-12" style="text-align: left !important;">
                <%= @lab_docket.disinfectant_further_information.presence %>
              </div>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
  <% length = @lab_docket_items.count %>
  <% if length != (idx + 1) %>
    <div class="pagebreak"> </div>
  <% end %>
<% end %>
