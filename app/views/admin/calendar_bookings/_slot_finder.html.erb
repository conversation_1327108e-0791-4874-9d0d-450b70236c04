<%= form_with model: @calendar_booking,
              url: admin_calendar_bookings_path,
              scope: 'calendar_booking',
              method: :post,
              local: true,
              id: 'slot-finder-form' do |f| %>

  <div class="w-full h-full flex flex-col items-start gap-4 pb-4">
    <div class="w-full flex items-center gap-2 mt-1">
      <%= f.select :patient_id, [], { prompt: 'Choose Patient' },
                    { required: true,
                      class: 'flex-1 w-full h-11 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-md',
                      id: 'slot-finder-patient-select' } %>
    </div>

    <div class="slot-finder-toggleable-section w-full flex flex-col items-start gap-4">
      <div id="slot-finder-selected-appointment-card"
           style="display: none;"
           class="w-full flex items-center justify-center">
      </div>

      <div id="reset-selection-container" class="hidden ml-auto">
        <button type="button"
                class="cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 hover:bg-gray-100 h-9 rounded-md px-3 text-[12px] text-gray-500 hover:text-gray-700"
                id="reset-selection-button">
          Reset Selection
        </button>
      </div>

      <div class="w-full flex items-center gap-2" id="slot-finder-practitioner-select-container">
        <%= f.select :practitioner_ids, [], { prompt: 'Choose Clinicians' },
                     { required: true,
                       multiple: true,
                       class: 'flex-1 w-full h-11 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-md select2-multiple',
                       id: 'slot-finder-practitioner-select' } %>
      </div>

      <div class="w-full flex items-center gap-2" id="slot-finder-treatment-select-container">
        <%= f.select :treatment_id, options_from_collection_for_select(@treatments, :id, :patient_friendly_name), { prompt: 'Choose Treatment' },
                     { required: true,
                       disabled: false,
                      class: 'flex-1 w-full h-11 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-md',
                      id: 'slot-finder-treatment-select' } %>
      </div>

      <div class="w-full flex gap-5">
        <div class="relative w-full flex-1">
          <%= f.date_field :start_date, value: Date.tomorrow, required: true,
                          class: 'w-full h-11 pr-10 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-md' %>
          <i class="fa-regular fa-calendar-days absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"></i>
        </div>

        <div class="relative w-full flex-1">
          <%= f.date_field :end_date, value: Date.tomorrow + 1.week, required: true,
                          class: 'w-full h-11 pr-10 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-md' %>
          <i class="fa-regular fa-calendar-days absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"></i>
        </div>
      </div>

      <div class="w-full flex gap-5">
        <%= f.time_field :start_time, value: '08:00', required: true, step: 900,
                         class: 'w-full h-11 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-md' %>

        <%= f.time_field :end_time, value: '20:00', required: true, step: 900,
                         class: 'w-full h-11 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-md' %>

        <%= f.select :duration_in_munutes, duration_in_minutes_options_for_select(default_value: 30), { prompt: 'Choose Duration' },
              { required: true, class: 'w-full px-2 h-11 text-sm text-gray-700 bg-white border border-gray-200 rounded-md' } %>
      </div>

      <div class="w-full flex flex-col items-start gap-1" id="calendar-booking-notes-input">
        <%= f.text_area :notes, rows: 1, placeholder: 'Notes', class: 'w-full h-24 p-2 text-[14px] text-gray-700 resize-y bg-white border border-gray-200 rounded-md' %>
      </div>
    </div>
  </div>
<% end %>

<div id="find-slots-button" class="pt-4 mb-2 flex justify-center">
  <button class="cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-8 py-3 bg-[#a8d8ea] hover:bg-[#8fcbdf] text-black rounded-full font-medium transition-all text-[14px]">
    Find Available Slots
  </button>
</div>

<div id="back-to-appointments-button" class="pt-4 mb-2 flex justify-center hidden">
  <button class="cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-8 py-3 bg-[#a8d8ea] hover:bg-[#8fcbdf] text-black rounded-full font-medium transition-all text-[14px]">
    Back to Appointments
  </button>
</div>

<!-- NOTE: managed by JS -->
<div id="slots-container" class="hidden"></div>

<!-- NOTE: managed by JS -->
<div id="upcoming-appointments-container">
  <div id="upcoming-charting-appointments-section">
  </div>

  <div id="upcoming-recalls-section">
  </div>
</div>
