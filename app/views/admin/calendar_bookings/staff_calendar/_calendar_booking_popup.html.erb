<div class="booking-info-card p-2 w-full rounded-lg border border-gray-200 bg-white shadow-lg opacity-0 scale-95 transform transition duration-150 ease-out pointer-events-auto">
  <div class="bg-white rounded-lg p-3 mb-3 border border-gray-100 shadow-sm overflow-hidden">
    <div class="relative pb-2 mb-2 border-b border-gray-100 after:content-[''] after:absolute after:bottom-[-1px] after:left-0 after:w-1/3 after:h-[2px] after:bg-gradient-to-r after:from-blue-400 after:to-indigo-500">
      <div class="flex items-center justify-between">
        <h3 class="text-[13px] font-semibold text-gray-800 tracking-tight">Patient Profile</h3>

        <% case @balance_status %>
        <% when 'Clear' %>
          <% background_color = '#BDCFDB' %>
          <% text_color = '#2A4558' %>
        <% when 'Overdue' %>
          <% background_color = 'rgba(185, 113, 114, 0.20)' %>
          <% text_color = '#B97172' %>
        <% when 'Outstanding' %>
          <% background_color = 'rgba(251, 224, 153, var(--bg-opacity, 1))' %>
          <% text_color = '#2A4558' %>
        <% end %>

        <div class="flex items-center">
          <div class="w-2 h-2 rounded-full mr-1.5" style="background-color: <%= text_color %>;"></div>

          <span class="font-medium text-[14px] text-gray-800">
            <%= number_to_currency(@balance, unit: "£") %>
          </span>

          <span class="ml-1.5 text-[10px] px-1.5 py-0.5 rounded-sm" style="background-color: <%= background_color %>; color: <%= text_color %>;">
            <%= @balance_status %>
          </span>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-2 gap-x-4 gap-y-2">
      <div class="flex items-center">
        <div class="w-6 h-6 rounded-full bg-gradient-to-br from-blue-100 to-blue-50 flex items-center justify-center mr-1.5 flex-shrink-0">
          <svg class="w-3 h-3 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
        </div>
        <div class="overflow-hidden"><span class="text-[11px] text-gray-500 block leading-none">Name</span><span class="font-medium text-[13px] text-gray-800 truncate block"><%= @booking.patient.full_name_with_title %></span></div>
      </div>
      <div class="flex items-center">
        <div class="w-6 h-6 rounded-full bg-gradient-to-br from-indigo-100 to-indigo-50 flex items-center justify-center mr-1.5 flex-shrink-0">
          <svg class="w-3 h-3 text-indigo-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
        </div>
        <div class="overflow-hidden"><span class="text-[11px] text-gray-500 block leading-none">DOB</span><span class="font-medium text-[13px] text-gray-800 block"><%= @booking.patient.date_of_birth&.strftime('%-d/%-m/%Y').presence || '-' %></span></div>
      </div>
      <div class="flex items-center col-span-2">
        <div class="w-6 h-6 rounded-full bg-gradient-to-br from-purple-100 to-purple-50 flex items-center justify-center mr-1.5 flex-shrink-0">
          <svg class="w-3 h-3 text-purple-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
          </svg>
        </div>
        <div class="overflow-hidden"><span class="text-[11px] text-gray-500 block leading-none">Phone</span><span class="font-medium text-[13px] text-gray-800 block"><%= @booking.patient.mobile_phone.presence || '-' %></span></div>
      </div>
      <div class="flex items-center col-span-2">
        <div class="w-6 h-6 rounded-full bg-gradient-to-br from-green-100 to-green-50 flex items-center justify-center mr-1.5 flex-shrink-0">
          <svg class="w-3 h-3 text-green-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
            <polyline points="22,6 12,13 2,6"></polyline>
          </svg>
        </div>
        <div class="overflow-hidden"><span class="text-[11px] text-gray-500 block leading-none">Email</span><span class="font-medium text-[13px] text-gray-800 truncate block"><%= @booking.patient.email.presence || '-' %></span></div>
      </div>
    </div>
  </div>
  <div class="bg-white rounded-lg p-3 border border-blue-100 shadow-sm overflow-hidden">
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-4 h-4 mr-2 text-blue-600">
          <path d="M8 2v4"></path>
          <path d="M16 2v4"></path>
          <rect width="18" height="18" x="3" y="4" rx="2"></rect>
          <path d="M3 10h18"></path>
        </svg>
        <h3 class="text-[13px] font-semibold text-gray-800 tracking-tight">Appointment Details</h3>
      </div>
    </div>
    <div class="grid grid-cols-2 gap-4 mb-3">
      <div>
        <span class="text-[11px] text-gray-500 block mb-1">Treatment</span>
        <div class="flex items-center">
          <div class="px-2.5 py-1 rounded-full bg-blue-100 text-[12px] font-medium text-blue-700 inline-flex items-center">
            <%= @booking.treatment&.patient_friendly_name.presence&.truncate(20) || 'Other' %>
          </div>
        </div>
      </div>
      <div>
        <span class="text-[11px] text-gray-500 block mb-1">Duration</span>
        <div class="flex items-center">
          <div class="px-2.5 py-1 rounded-full bg-orange-100 text-[12px] font-medium text-orange-700 inline-flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-3 h-3 mr-1">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            <span><%= @booking.duration_in_minutes %> mins</span>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-2 pt-2 border-t border-blue-200">
      <div class="flex items-center mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-3.5 h-3.5 mr-1.5 text-blue-600">
          <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
          <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
          <path d="M10 9H8"></path>
          <path d="M16 13H8"></path>
          <path d="M16 17H8"></path>
        </svg>
        <div class="text-[13px] font-medium text-gray-700">Notes</div>
      </div>
      <p class="font-medium text-gray-700 text-[12px] leading-tight"><%= @booking.notes.presence || '-' %></p>
    </div>

    <% charting_appointment = @booking.charting_appointments.last %>
    <% icon_state = @booking.icon_state %>

    <!-- Lab Work -->
    <div class="pt-2 mt-2">
      <% if charting_appointment&.lab_works&.any? %>
        <div class="bg-blue-50 rounded-lg p-2 mb-2 border border-blue-100">
          <div class="text-[12px] font-medium text-gray-700 mb-2 flex items-center">
            <svg class="lucide lucide-test-tube w-3.5 h-3.5 mr-1.5 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2"></path><path d="M8.5 2h7"></path><path d="M14.5 16h-5"></path></svg>Lab Work
          </div>
          <% lab = charting_appointment.lab_works.last %>
          <div class="flex items-center justify-between">
            <span class="text-[12px] px-2 py-0.5 rounded-full <%= status_badge_class(icon_state&.lab_work_state) %>"><%= lab.status %></span>
          </div>
        </div>
      <% else %>
        <div class="bg-gray-50 rounded-lg p-2 mb-2 border border-gray-200 opacity-60">
          <div class="text-[12px] font-medium text-gray-500 mb-2 flex items-center">
            <svg class="lucide lucide-test-tube w-3.5 h-3.5 mr-1.5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2"></path><path d="M8.5 2h7"></path><path d="M14.5 16h-5"></path></svg>Lab Work
          </div>
          <div class="flex items-center justify-between">
            <span class="font-medium text-gray-500 text-[12px]">No Lab Work</span>
            <span class="text-[12px] px-2 py-0.5 rounded-full bg-gray-200 text-gray-500">N/A</span>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Documents -->
    <div class="pt-2 mt-2">
      <% sigs = charting_appointment&.signature_requests || [] %>
      <% if sigs.any? %>
        <div class="bg-indigo-50 rounded-lg p-2 mb-2 border border-indigo-100">
          <div class="text-[12px] font-medium text-gray-700 mb-2 flex items-center">
            <svg class="lucide lucide-files w-3.5 h-3.5 mr-1.5 text-indigo-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 7h-3a2 2 0 0 1-2-2V2"></path><path d="M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z"></path><path d="M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8"></path></svg>Documents
          </div>
          <div class="flex items-center justify-between">
            <span class="font-medium text-gray-800 text-[12px]"><%= "#{sigs.count(&:signed?)}/#{sigs.size} signed" %></span>
            <span class="text-[12px] px-2 py-0.5 rounded-full <%= status_badge_class(icon_state&.documents_state) %>"><%= icon_state&.documents_state&.to_s&.titleize %></span>
          </div>
        </div>
      <% else %>
        <div class="bg-gray-50 rounded-lg p-2 mb-2 border border-gray-200 opacity-60">
          <div class="text-[12px] font-medium text-gray-500 mb-2 flex items-center">
            <svg class="lucide lucide-files w-3.5 h-3.5 mr-1.5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 7h-3a2 2 0 0 1-2-2V2"></path><path d="M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z"></path><path d="M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8"></path></svg>Documents
          </div>
          <div class="flex items-center justify-between">
            <span class="font-medium text-gray-500 text-[12px]">No Documents</span>
            <span class="text-[12px] px-2 py-0.5 rounded-full bg-gray-200 text-gray-500">N/A</span>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Patient Issues -->
    <div class="pt-2 mt-2">
      <% complaints = @booking.patient.actions.complaints %>
      <% if complaints.any? %>
        <div class="bg-purple-50 rounded-lg p-2 mb-2 border border-purple-100">
          <div class="text-[12px] font-medium text-gray-700 mb-2 flex items-center">
            <svg class="lucide lucide-circle-alert w-3.5 h-3.5 mr-1.5 text-purple-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>Patient Issues
          </div>
          <div class="font-medium text-gray-700 text-[12px] leading-tight"><%= complaints.last.description %></div>
        </div>
      <% else %>
        <div class="bg-gray-50 rounded-lg p-2 mb-2 border border-gray-200 opacity-60">
          <div class="text-[12px] font-medium text-gray-500 mb-2 flex items-center">
            <svg class="lucide lucide-circle-alert w-3.5 h-3.5 mr-1.5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>Patient Issues
          </div>
          <div class="flex items-center justify-between">
            <span class="font-medium text-gray-500 text-[12px]">No Issues</span>
            <span class="text-[12px] px-2 py-0.5 rounded-full bg-gray-200 text-gray-500">N/A</span>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Medical History -->
    <div class="pt-2 mt-2">
      <% history_state = icon_state&.medical_history_state %>
      <% if @booking.patient.medical_histories.any? %>
        <div class="bg-teal-50 rounded-lg p-2 mb-2 border border-teal-100">
          <div class="text-[12px] font-medium text-gray-700 mb-2 flex items-center">
            <svg class="lucide lucide-activity w-3.5 h-3.5 mr-1.5 text-teal-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M22 12h-2.48a2 2 0 0 1-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"></path></svg>Medical History
          </div>
          <% last = @booking.patient.medical_histories.last %>
          <% days_ago = (Date.current - last.created_at.to_date).to_i %>
          <div class="font-medium text-gray-700 text-[12px] leading-tight">
            <%= history_state == 'positive' ? 'Completed today' : "Medical history last completed #{days_ago} days ago" %>
          </div>
        </div>
      <% else %>
        <div class="bg-gray-50 rounded-lg p-2 mb-2 border border-gray-200 opacity-60">
          <div class="text-[12px] font-medium text-gray-500 mb-2 flex items-center">
            <svg class="lucide lucide-activity w-3.5 h-3.5 mr-1.5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M22 12h-2.48a2 2 0 0 1-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"></path></svg>Medical History
          </div>
          <div class="flex items-center justify-between">
            <span class="font-medium text-gray-500 text-[12px]">No Record</span>
            <span class="text-[12px] px-2 py-0.5 rounded-full bg-gray-200 text-gray-500">N/A</span>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Patient Balance -->
    <div class="pt-2 mt-2">
      <div class="bg-gray-50 rounded-lg p-2 mb-2 border border-gray-200">
        <div class="text-[12px] font-medium text-gray-700 mb-2 flex items-center">
          <svg class="lucide lucide-wallet w-3.5 h-3.5 mr-1.5 text-gray-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 12V7H5a2 2 0 0 1 0-4h14v4"></path><path d="M3 5v14a2 2 0 0 0 2 2h16v-5"></path><path d="M18 12a2 2 0 0 0-2 2v1H9v-1a2 2 0 0 0-2-2"></path></svg>Patient Balance
        </div>
        <div class="flex items-center justify-between">
          <div class="font-medium text-gray-700 text-[12px] leading-tight">
            Current balance
            <br>
            <span class="text-gray-800 font-semibold"><%= number_to_currency(@balance, unit: "£") %></span>
          </div>
          <span class="text-[12px] px-2 py-0.5 rounded-full" style="background-color: <%= background_color %>; color: <%= text_color %>;">
            <%= @balance_status %>
          </span>
        </div>
      </div>
    </div>

    <!-- Payment Request -->
    <div class="pt-2 mt-2">
      <% if @booking.payment_request_status.present? && @booking.payment_request_status != 'not_requested' %>
        <div class="bg-amber-50 rounded-lg p-2 mb-2 border border-amber-100">
          <div class="text-[12px] font-medium text-gray-700 mb-2 flex items-center">
            <svg class="lucide lucide-credit-card w-3.5 h-3.5 mr-1.5 text-amber-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect width="20" height="14" x="2" y="5" rx="2"></rect><line x1="2" x2="22" y1="10" y2="10"></line></svg>Payment Request
          </div>
          <div class="flex items-center justify-between">
            <div class="font-medium text-gray-700 text-[12px] leading-tight">
              <% if @booking.payment_request_status == 'paid' %>
                Payment received
              <% else %>
                Payment pending
              <% end %>
              <br>
              <span class="text-amber-700 font-semibold">£<%= number_with_precision(@booking.payment_request_amount, precision: 2) %></span>
            </div>
            <span class="text-[12px] px-2 py-0.5 rounded-full <%= @booking.payment_request_status == 'paid' ? 'bg-green-100 text-green-700' : 'bg-amber-100 text-amber-700' %>">
              <%= @booking.payment_request_status.titleize %>
            </span>
          </div>
        </div>
      <% else %>
        <div class="bg-gray-50 rounded-lg p-2 mb-2 border border-gray-200 opacity-60">
          <div class="text-[12px] font-medium text-gray-500 mb-2 flex items-center">
            <svg class="lucide lucide-credit-card w-3.5 h-3.5 mr-1.5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect width="20" height="14" x="2" y="5" rx="2"></rect><line x1="2" x2="22" y1="10" y2="10"></line></svg>Payment Request
          </div>
          <div class="flex items-center justify-between">
            <span class="font-medium text-gray-500 text-[12px]">No payment request</span>
            <span class="text-[12px] px-2 py-0.5 rounded-full bg-gray-200 text-gray-500">N/A</span>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
