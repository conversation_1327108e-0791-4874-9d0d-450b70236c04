(function() {
  openOffcanvas('reschedule-offcanvas');
  document.getElementById('reschedule-offcanvas-container').innerHTML = "<%= j render partial: 'reschedule_calendar_booking', patients: @patients, dentists: @practitioners %>";

  const patientSelect = $('#reschedule-patient-select');
  const practitionerSelect = $('#reschedule-practitioner-select');

  let currentDate = "<%= Date.tomorrow.strftime('%Y-%m-%d') %>";

  fetchSlots();

  $("#find-slots-button").on('click', function() {
    fetchSlots();
  })

  $('input[name="calendar_booking[start_date]"]').on('change', function () {
    const startDateValue = this.value;
    const $endDateInput = $('input[name="calendar_booking[end_date]"]');
    const endDateValue = $endDateInput.val();

    if (startDateValue && endDateValue) {
      const startDate = new Date(startDateValue);
      const endDate = new Date(endDateValue);

      if (startDate > endDate) {
        const newEndDate = new Date(startDate);
        newEndDate.setMonth(newEndDate.getMonth() + 1);
        $endDateInput.val(newEndDate.toISOString().split('T')[0]);
      }
    }

    currentDate = this.value;

    fetchSlots();
  });
  $('input[name="calendar_booking[start_time]"]').on('change', fetchSlots);
  $('input[name="calendar_booking[end_time]"]').on('change', fetchSlots);
  $('select[name="calendar_booking[duration_in_munutes]"]').on('change', fetchSlots);

  patientSelect.on('change', function() {
    fetchSlots();
  });

  function fetchSlots() {
    const dentistId = practitionerSelect.val();
    const startTime = document.querySelector('input[name="calendar_booking[start_time]"]').value;
    const endTime = document.querySelector('input[name="calendar_booking[end_time]"]').value;
    const duration = document.querySelector('select[name="calendar_booking[duration_in_munutes]"]').value;

    if (!dentistId || !startTime || !endTime || !duration) {
      return;
    }

    const data = {
      practitioner_ids: dentistId,
      date: currentDate,
      start_time: startTime,
      end_time: endTime,
      duration: duration,
    };

    $.ajax({
      url: '/admin/calendar_bookings/slots',
      data: data,
      success: function(response) {
        document.getElementById('reschedule-slots-container').innerHTML = response;
        handleBookNowButton();

        $('#slots-prev-day-button').on('click', () => adjustDate(-1));
        $('#slots-next-day-button').on('click', () => adjustDate(1));
        $('#slots-plus-1w-button').on('click', () => adjustDate(7));
        $('#slots-plus-3m-button').on('click', () => adjustDate(90));
        $('#slots-plus-6m-button').on('click', () => adjustDate(180));

        $('#slots-today-button').on('click', () => {
          currentDate = new Date().toISOString().split('T')[0];
          $('#current-date-label').text(new Date(currentDate).toDateString());

          fetchSlots();
        });

        function adjustDate(delta) {
          const dateObj = new Date(currentDate);
          dateObj.setDate(dateObj.getDate() + delta);
          currentDate = dateObj.toISOString().split('T')[0];

          $('#reschedule-current-date-label').text(dateObj.toDateString());

          fetchSlots();
        }
      }
    });

    function handleBookNowButton() {
      const bookNowButtons = document.querySelectorAll('.slots-book-now-button');
      bookNowButtons.forEach(function (button) {
        button.addEventListener('click', function (event) {
          event.preventDefault();

          const slotStart = this.getAttribute('data-slot-start');
          const slotEnd = this.getAttribute('data-slot-end');
          const slotDate = this.getAttribute('data-slot-date');
          const practitionerId = practitionerSelect.val();
          const patientId = patientSelect.val();
          const mode = '<%= @mode %>';
          const date = '<%= @date %>';

          if (!practitionerId || !patientId) {
            alert("Please select a clinician and a patient.");
            return;
          }

          const form = document.createElement('form');
          form.action = '<%= admin_calendar_booking_path(@booking) %>';
          form.method = 'POST';
          form.appendChild(createHiddenInput('_method', 'patch'));

          const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
          const csrfInput = document.createElement('input');
          csrfInput.type = 'hidden';
          csrfInput.name = 'authenticity_token';
          csrfInput.value = csrfToken;
          form.appendChild(csrfInput);

          form.appendChild(createHiddenInput('calendar_booking[practitioner_id]', practitionerId));
          form.appendChild(createHiddenInput('calendar_booking[start_time]', slotStart));
          form.appendChild(createHiddenInput('calendar_booking[end_time]', slotEnd));
          form.appendChild(createHiddenInput('calendar_booking[date]', slotDate));
          form.appendChild(createHiddenInput('mode', mode));
          form.appendChild(createHiddenInput('date', date));

          document.body.appendChild(form);
          form.submit();
        });
      });

      function createHiddenInput(name, value) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = name;
        input.value = value;
        return input;
      }
    }
  }


  function handleChartingAppointments() {
    // TODO?
    document.addEventListener('click', e => {
      if (!e.target.closest('#reschedule-offcanvas-container')) return;

      const card = e.target.closest('[data-booking-id]');
      if (!card) return;
      const confirm = card.dataset.previousBooked !== 'true';
      (confirm ? Swal.fire({
          title:'Are you sure?',
          text:'The previous appointment in that treatment plan is not booked yet. Are you sure you want continue?',
          icon:'warning',
          showCancelButton:true,
          confirmButtonText:'Yes, continue',
          cancelButtonText:'No, cancel',
          reverseButtons:true,
          returnFocus: false
        }).then(r=>r.isConfirmed && selectCard(card))
        : selectCard(card));
    });
  }
})();
