(function() {
  openOffcanvas('slot-finder-offcanvas');
  document.getElementById('slot-finder-offcanvas-container').innerHTML = "<%= j render partial: 'slot_finder', patients: @patients, dentists: @practitioners %>";

  const patientSelect = $('#slot-finder-patient-select');
  const practitionerSelect = $('#slot-finder-practitioner-select');
  const treatmentSelect = $('#slot-finder-treatment-select');

  let currentDate = "<%= Date.tomorrow.strftime('%Y-%m-%d') %>";

  patientSelect.select2({
    theme: 'tailwindcss-3',
    allowClear: false,
    selectionCssClass: 'form-control',
    dropdownCssClass: 'form-control',
    placeholder: 'Search for a patient',
    minimumInputLength: 3,
    width: '100%',
    ajax: {
      url: '/admin/patients/select2_search',
      dataType: 'json',
      delay: 250,
      data: function (params) {
        return {
          q: params.term
        };
      },
      processResults: function (data, params) {
        return data;
      }
    }
  });

  practitionerSelect.select2({
    theme: 'tailwindcss-3',
    allowClear: false,
    selectionCssClass: 'form-control',
    dropdownCssClass: 'form-control',
    placeholder: 'Search for practitioners',
    minimumInputLength: 3,
    width: '100%',
    ajax: {
      url: '/admin/general_settings/users/select2_search',
      dataType: 'json',
      delay: 250,
      data: function (params) {
        return {
          q: params.term
        };
      },
      processResults: function (data, params) {
        return data;
      }
    }
  });

  treatmentSelect.select2({
    theme: 'tailwindcss-3',
    allowClear: false,
    selectionCssClass: 'form-control',
    dropdownCssClass: 'form-control',
    placeholder: 'Search for a treatment',
    minimumInputLength: 3,
    width: '100%',
  });

  $("#find-slots-button").on('click', function() {
    if (!patientSelect.val()) {
      Swal.fire('Patient Not Selected', 'Please select a patient first to find available slots.', 'info');
      return;
    }

    if (!practitionerSelect.val() || practitionerSelect.val().length === 0) {
      Swal.fire('Practitioners Not Selected', 'Please select at least one practitioner to find available slots.', 'info');
      return;
    }

    fetchSlots();
    showSlots();
  })

  $('#back-to-appointments-button').on('click', function() {
    if (!patientSelect.val()) {
      Swal.fire('Patient Not Selected', 'Please select a patient first to view their upcoming charting appointments and recalls.', 'info');
      return;
    }

    fetchUpcomingAppointments();
    showUpcomingAppointments();
  });

  $('input[name="calendar_booking[start_date]"]').on('change', function () {
    const startDateValue = this.value;
    const $endDateInput = $('input[name="calendar_booking[end_date]"]');
    const endDateValue = $endDateInput.val();

    if (startDateValue && endDateValue) {
      const startDate = new Date(startDateValue);
      const endDate = new Date(endDateValue);

      if (startDate > endDate) {
        const newEndDate = new Date(startDate);
        newEndDate.setMonth(newEndDate.getMonth() + 1);
        $endDateInput.val(newEndDate.toISOString().split('T')[0]);
      }
    }

    currentDate = this.value;

    fetchSlots();
  });
  $('input[name="calendar_booking[start_time]"]').on('change', fetchSlots);
  $('input[name="calendar_booking[end_time]"]').on('change', fetchSlots);
  $('select[name="calendar_booking[duration_in_munutes]"]').on('change', fetchSlots);
  practitionerSelect.on('change', fetchSlots);

  function showSlots() {
    $('#back-to-appointments-button').removeClass('hidden');
    $('#find-slots-button').addClass('hidden');

    $('#slots-container').removeClass('hidden');
    $('#upcoming-appointments-container').addClass('hidden');
  }

  function showUpcomingAppointments() {
    $('#find-slots-button').removeClass('hidden');
    $('#back-to-appointments-button').addClass('hidden');

    $('#upcoming-appointments-container').removeClass('hidden');
    $('#slots-container').addClass('hidden');
  }

  patientSelect.on('change', function() {
    fetchSlots();
    fetchUpcomingAppointments();

    const form = document.getElementById('slot-finder-form');
    const existingInput = form.querySelector('input[name="calendar_booking[charting_appointment_id]"]');
    if (existingInput) {
      form.removeChild(existingInput);
    }
    document.getElementById('slot-finder-selected-appointment-card').style.display = 'none';
    document.getElementById('slot-finder-practitioner-select-container').style.display = 'block';
    document.getElementById('slot-finder-treatment-select-container').style.display = 'block';
    document.getElementById('calendar-booking-notes-input').style.display = 'block';
  });

  function fetchSlots() {
    const practitionerIds = practitionerSelect.val();
    const startTime = document.querySelector('input[name="calendar_booking[start_time]"]').value;
    const endTime = document.querySelector('input[name="calendar_booking[end_time]"]').value;
    const duration = document.querySelector('select[name="calendar_booking[duration_in_munutes]"]').value;

    if (!practitionerIds || practitionerIds.length === 0 || !startTime || !endTime || !duration) {
      return;
    }

    const data = {
      practitioner_ids: practitionerIds,
      date: currentDate,
      start_time: startTime,
      end_time: endTime,
      duration: duration,
    };

    // TODO: doesn't work
    const searchResultCard = document.querySelector('.search-result-card[data-booking-id]');
    if (searchResultCard && searchResultCard.offsetWidth > 0 && searchResultCard.offsetHeight > 0) { // visible
      const chartingAppointmentId = searchResultCard.getAttribute('data-booking-id');
      if (chartingAppointmentId) {
        data.charting_appointment_id = chartingAppointmentId;
      }
    }

    $.ajax({
      url: '/admin/calendar_bookings/slots',
      data: data,
      success: function(response) {
        document.getElementById('slots-container').innerHTML = response;
        handleBookNowButton();

        $('#slots-prev-day-button').on('click', () => adjustDate(-1));
        $('#slots-next-day-button').on('click', () => adjustDate(1));
        $('#slots-plus-1w-button').on('click', () => adjustDate(7));
        $('#slots-plus-3m-button').on('click', () => adjustDate(90));
        $('#slots-plus-6m-button').on('click', () => adjustDate(180));

        $('#slots-today-button').on('click', () => {
          currentDate = new Date().toISOString().split('T')[0];
          $('#current-date-label').text(new Date(currentDate).toDateString());

          fetchSlots();
        });

        function adjustDate(delta) {
          const dateObj = new Date(currentDate);
          dateObj.setDate(dateObj.getDate() + delta);
          currentDate = dateObj.toISOString().split('T')[0];

          $('#slot-finder-current-date-label').text(dateObj.toDateString());

          fetchSlots();
        }
      }
    });

    function handleBookNowButton() {
      const bookNowButtons = document.querySelectorAll('.slots-book-now-button');
      bookNowButtons.forEach(function (button) {
        button.addEventListener('click', function (event) {
          event.preventDefault();

          const slotStart = this.getAttribute('data-slot-start');
          const slotEnd = this.getAttribute('data-slot-end');
          const slotDate = this.getAttribute('data-slot-date');
          const practitionerIds = practitionerSelect.val();
          const patientId = patientSelect.val();
          const notes = document.querySelector('textarea[name="calendar_booking[notes]"]').value;
          const treatmentId = document.querySelector('select[name="calendar_booking[treatment_id]"]').value;
          const chartingAppointmentId = document.querySelector('input[name="calendar_booking[charting_appointment_id]"]')?.value;
          const recallId = document.querySelector('input[name="calendar_booking[recall_id]"]')?.value;
          const mode = '<%= @mode %>';
          const date = '<%= @date %>';

          if (!practitionerIds || practitionerIds.length === 0 || !patientId) {
            alert("Please select at least one clinician and a patient.");
            return;
          }

          const form = document.createElement('form');
          form.action = '<%= admin_calendar_bookings_path %>';
          form.method = 'POST';

          const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
          const csrfInput = document.createElement('input');
          csrfInput.type = 'hidden';
          csrfInput.name = 'authenticity_token';
          csrfInput.value = csrfToken;
          form.appendChild(csrfInput);

          form.appendChild(createHiddenInput('calendar_booking[practitioner_id]', practitionerIds[0]));
          form.appendChild(createHiddenInput('calendar_booking[patient_id]', patientId));
          form.appendChild(createHiddenInput('calendar_booking[notes]', notes));
          form.appendChild(createHiddenInput('calendar_booking[start_time]', slotStart));
          form.appendChild(createHiddenInput('calendar_booking[end_time]', slotEnd));
          form.appendChild(createHiddenInput('calendar_booking[date]', slotDate));
          form.appendChild(createHiddenInput('calendar_booking[treatment_id]', treatmentId));
          form.appendChild(createHiddenInput('mode', mode));
          form.appendChild(createHiddenInput('date', date));

          if (chartingAppointmentId) {
            form.appendChild(createHiddenInput('calendar_booking[charting_appointment_id]', chartingAppointmentId));
          }

          if (recallId) {
            form.appendChild(createHiddenInput('calendar_booking[recall_id]', recallId));
          }

          document.body.appendChild(form);
          form.submit();
        });
      });

      function createHiddenInput(name, value) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = name;
        input.value = value;
        return input;
      }
    }
  }

  function scrollContainerToTop() {
    const container = document.getElementById('slot-finder-offcanvas-container');
    if (container) {
      container.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  }

  function fetchUpcomingAppointments() {
    const patientId = patientSelect.val();

    if (!patientId) {
      return;
    }

    $.ajax({
      url: '/admin/calendar_bookings/charting_appointments/upcoming',
      data: { patient_id: patientId },
      success: function(response) {
        document.getElementById('upcoming-charting-appointments-section').innerHTML = response;
        handleChartingAppointments();
      }
    });

    $.ajax({
      url: '/admin/calendar_bookings/recalls/upcoming',
      data: { patient_id: patientId },
      success: function(response) {
        document.getElementById('upcoming-recalls-section').innerHTML = response;
        handleRecalls();
      }
    });
  }

  function handleChartingAppointments() {
    const fixedDurations = [15,20,25,30,45,60,75,90,105,120,180,240];
    const roundDuration  = d => fixedDurations.find(fd => d <= fd) || fixedDurations.at(-1);
    const ringColorClass = 'ring-[#3b82f6]';

    const selectionContainer = document.getElementById('slot-finder-selected-appointment-card');
    const resetWrapper       = document.getElementById('reset-selection-container');
    const resetBtn           = document.getElementById('reset-selection-button');

    let lastCard = null;

    const hiddenInput = (n,v)=>Object.assign(document.createElement('input'),{type:'hidden',name:n,value:v});

    resetBtn.addEventListener('click', () => {
      const form = document.getElementById('slot-finder-form');
      form.querySelectorAll('input[name="calendar_booking[charting_appointment_id]"]').forEach(i=>i.remove());

      document.querySelectorAll('[data-booking-id].selected-active').forEach(c=>c.classList.remove('ring-2',ringColorClass,'bg-[#bfdbfe]','selected-active'));

      lastCard?.blur?.(); lastCard = null;

      selectionContainer.innerHTML = '';
      selectionContainer.style.display = 'none';
      resetWrapper.classList.add('hidden');

      document.getElementById('slot-finder-practitioner-select-container').style.display = 'block';
      document.getElementById('slot-finder-treatment-select-container').style.display = 'block';
      document.getElementById('calendar-booking-notes-input').style.display = 'block';
    });

    document.addEventListener('click', e => {
      if (!e.target.closest('#slot-finder-offcanvas-container')) return;

      const card = e.target.closest('[data-booking-id]');
      if (!card) return;
      const confirm = card.dataset.previousBooked !== 'true';
      (confirm ? Swal.fire({
          title:'Are you sure?',
          text:'The previous appointment in that treatment plan is not booked yet. Are you sure you want continue?',
          icon:'warning',
          showCancelButton:true,
          confirmButtonText:'Yes, continue',
          cancelButtonText:'No, cancel',
          reverseButtons:true,
          returnFocus: false
        }).then(r=>r.isConfirmed && selectCard(card))
        : selectCard(card));
    });

    function selectCard(card){
      const duration     = roundDuration(parseInt(card.dataset.duration,10)||30);
      const bookableFrom = card.dataset.bookableFrom;
      const id           = card.dataset.bookingId;

      const form = document.getElementById('slot-finder-form');
      form.querySelectorAll('input[name="calendar_booking[charting_appointment_id]"]').forEach(el=>el.remove());
      form.appendChild(hiddenInput('calendar_booking[charting_appointment_id]',id));

      const parent=card.parentElement;
      const practitionerId   = parent.dataset.practitionerId;
      const practitionerName = parent.dataset.practitionerName;
      const practitionerSel  = $('#slot-finder-practitioner-select');

      const sDate = document.querySelector('input[name="calendar_booking[start_date]"]');
      const eDate = document.querySelector('input[name="calendar_booking[end_date]"]');
      if (sDate && eDate){
        sDate.value = bookableFrom;
        const d = new Date(bookableFrom); d.setMonth(d.getMonth()+1);
        eDate.value = d.toISOString().split('T')[0];
        sDate.dispatchEvent(new Event('change',{bubbles:true}));
      }

      if (practitionerId && practitionerName && practitionerSel.length){
        document.getElementById('slot-finder-practitioner-select-container').style.display='none';
        practitionerSel.append(new Option(practitionerName,practitionerId,true,true)).trigger('change');
      } else {
        document.getElementById('slot-finder-practitioner-select-container').style.display='block';
      }

      const durSel = document.querySelector('select[name="calendar_booking[duration_in_munutes]"]');
      if (durSel) durSel.value = duration;

      document.getElementById('slot-finder-treatment-select-container').style.display='none';
      document.getElementById('calendar-booking-notes-input').style.display='none';

      document.querySelectorAll('[data-booking-id].selected-active').forEach(c=>c.classList.remove('ring-2',ringColorClass,'bg-[#bfdbfe]','selected-active'));
      card.classList.add('ring-2',ringColorClass,'bg-[#bfdbfe]','selected-active');

      const clone = card.cloneNode(true);
      clone.style.pointerEvents='none';
      clone.removeAttribute('data-booking-id');
      clone.tabIndex=-1;
      clone.classList.add('ring-2',ringColorClass,'bg-[#bfdbfe]');
      selectionContainer.innerHTML='';
      selectionContainer.appendChild(clone);
      selectionContainer.style.display='block';
      resetWrapper.classList.remove('hidden');

      lastCard = card;

      scrollContainerToTop();
    }

    <% if @charting_appointment && @charting_appointment_patient %>
      const autoCard = document.querySelector('[data-booking-id="<%= @charting_appointment.id %>"]');
      if (autoCard) {
        autoCard.dispatchEvent(new Event('click', { bubbles: true }));
        showSlots();
      }
    <% end %>
  }

  function handleRecalls() {
    const fixedDurations = [15,20,25,30,45,60,75,90,105,120,180,240];
    const roundDuration  = d => fixedDurations.find(fd => d <= fd) || fixedDurations.at(-1);
    const ringColorClass = 'ring-[#3b82f6]';

    const selectionContainer = document.getElementById('slot-finder-selected-appointment-card');
    const resetWrapper       = document.getElementById('reset-selection-container');

    const hiddenInput = (n,v)=>Object.assign(document.createElement('input'),{type:'hidden',name:n,value:v});

    document.addEventListener('click', e => {
      const card = e.target.closest('[data-recall-id]');
      if (!card) return;
      selectRecall(card);
    });

    function selectRecall(card) {
      const duration     = roundDuration(parseInt(card.dataset.duration,10) || 30);
      const recallId     = card.dataset.recallId;
      const practitionerId   = card.parentElement.dataset.practitionerId;
      const practitionerName = card.parentElement.dataset.practitionerName;
      const treatmentId  = card.dataset.treatmentId;

      const form = document.getElementById('slot-finder-form');
      ['calendar_booking[recall_id]','calendar_booking[charting_appointment_id]'].forEach(n=>{
        form.querySelectorAll(`input[name="${n}"]`).forEach(el=>el.remove());
      });
      form.appendChild(hiddenInput('calendar_booking[recall_id]',recallId));

      if (treatmentId) {
        const tSel = document.querySelector('select[name="calendar_booking[treatment_id]"]');
        if (tSel) tSel.value = treatmentId;
      }

      const practitionerSel = $('#slot-finder-practitioner-select');
      if (practitionerId && practitionerName && practitionerSel.length){
        document.getElementById('slot-finder-practitioner-select-container').style.display='none';
        practitionerSel.empty().append(new Option(practitionerName,practitionerId,true,true)).trigger('change');
      } else {
        document.getElementById('slot-finder-practitioner-select-container').style.display='block';
      }

      const durSel = document.querySelector('select[name="calendar_booking[duration_in_munutes]"]');
      if (durSel) durSel.value = duration;

      document.getElementById('slot-finder-treatment-select-container').style.display='none';
      document.getElementById('calendar-booking-notes-input').style.display='none';

      document.querySelectorAll('[data-recall-id].selected-active').forEach(c=>c.classList.remove('ring-2',ringColorClass,'bg-[#bfdbfe]','selected-active'));
      document.querySelectorAll('[data-booking-id].selected-active').forEach(c=>c.classList.remove('ring-2',ringColorClass,'bg-[#bfdbfe]','selected-active'));
      card.classList.add('ring-2',ringColorClass,'bg-[#bfdbfe]','selected-active');

      const clone = card.cloneNode(true);
      clone.style.pointerEvents='none';
      clone.removeAttribute('data-recall-id');
      clone.tabIndex=-1;
      clone.classList.add('ring-2',ringColorClass,'bg-[#bfdbfe]');
      selectionContainer.innerHTML='';
      selectionContainer.appendChild(clone);
      selectionContainer.style.display='block';
      resetWrapper.classList.remove('hidden');

      scrollContainerToTop();
    }
  }

  <% if @charting_appointment_patient %>
    var selected_patient_option = new Option("<%= @charting_appointment_patient.full_name_with_email %>", <%= @charting_appointment_patient.id %>, true, true);
    patientSelect.append(selected_patient_option).val(<%= @charting_appointment_patient.id %>).trigger('change');
  <% end %>
})();
