<div class="h-[calc(100vh-56px)] bg-[#f5f5f7] text-[#1d1d1f] font-sf-pro">
  <main class="w-full h-full overflow-hidden">
    <div class="w-full px-4 sm:px-6 lg:px-8 py-12">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 tracking-tight">Treatment Plans</h1>
        <p class="mt-2 text-base text-gray-600">A summary of all <%= @status %> patient treatment plans.</p>
      </div>

      <div class="relative w-full overflow-auto">
        <table class="w-full caption-bottom text-sm border-separate border-spacing-y-2">
          <thead class="[&amp;_tr]:border-b">
            <tr class="border-b transition-colors data-[state=selected]:bg-muted hover:bg-transparent">
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Treatment Envelope</th>
              <% if @status == 'completed' %>
                <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Signed Date</th>
                <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Accepted Amount</th>
              <% end %>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clinicians</th>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Treatment Options</th>
<!--              <th class="h-12 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0 px-6 py-3">-->
<!--                <span class="sr-only">Actions</span>-->
<!--              </th>-->
            </tr>
          </thead>

          <tbody class="[&_tr:last-child]:border-0">
            <% @treatment_plans.each do |plan| %>
              <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted bg-white rounded-lg shadow-sm">
                <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap rounded-l-lg align-top">
                  <div class="flex items-start">
                    <span class="relative flex shrink-0 overflow-hidden rounded-full h-10 w-10">
                      <% if plan.patient.image.attached? %>
                        <%= image_tag(plan.patient.image, class: "w-full h-full object-cover") %>
                      <% else %>
                        <div class="w-full h-full rounded-full bg-purple-800 text-white flex items-center justify-center text-sm font-medium border-2 border-white">
                          <%= plan.patient.first_name.first %><%= plan.patient.last_name.first %>
                        </div>
                      <% end %>
                    </span>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">
                        <%= link_to plan.patient.full_name, admin_patient_url(plan.patient.id), class: 'hover:text-blue-600 transition-colors' %>
                      </div>
                      <div class="text-sm text-gray-500"><%= plan.patient.email %></div>
                      <div class="flex items-center text-sm text-gray-500 mt-2 space-x-4">
                        <div class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4 mr-1.5 text-gray-400">
                            <path d="M8 2v4"></path>
                            <path d="M16 2v4"></path>
                            <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                            <path d="M3 10h18"></path>
                          </svg>
                          <span><%= plan.patient.date_of_birth&.strftime("%d %b %Y") %></span>
                        </div>
                        <span class="bg-gray-100 text-gray-700 px-2 py-0.5 rounded-md text-xs font-medium"><%= age_in_years_and_months(plan.patient.date_of_birth) %></span>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap align-top">
                  <% color = %w[green amber blue purple cyan].sample %>
                  <div class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-<%= color %>-50 text-<%= color %>-700 border-<%= color %>-200/50 border font-semibold">
                    <%= plan.name %>
                  </div>
                </td>
                <% if @status == 'completed' %>
                  <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap text-sm text-gray-700 align-top"><%= plan.accepted_options.first.signed_at.strftime('%d/%m/%Y') %></td>
                  <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap text-sm text-gray-700 align-top"><%= number_to_currency(plan.accepted_options.first.course_of_treatment&.total_price, unit: "£") %></td>
                <% end %>
                <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap align-top flex">
                  <% plan.treatment_plan_options.each do |option| %>
                    <% unless option.draft %>
                      <% if option.treatment_coordinator.image.attached? %>
                        <%= image_tag(option.treatment_coordinator.image, class: "w-8 h-8 rounded-full mr-2", data: {tippy_content: option.treatment_coordinator.full_name }) %>
                      <% else %>
                        <div class="w-8 h-8 rounded-full bg-purple-800 text-white flex items-center justify-center text-sm font-medium border-2 border-white mr-2" data-tippy-content="<%= option.treatment_coordinator.full_name %>">
                          <%= option.treatment_coordinator.first_name.first %><%= option.treatment_coordinator.last_name.first %>
                        </div>
                      <% end %>
                    <% end %>
                  <% end %>
                </td>
                <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap align-top">
                  <div class="flex items-center space-x-3">
                    <% plan.treatment_plan_options.each do |option| %>
                      <% unless option.draft %>
                        <% if option.status == 'Accepted' %>
                          <a href="<%= option.signed_pdf_file.url %>"
                             target="_blank" class="flex items-center justify-center h-8 w-8 rounded-full bg-green-100 text-green-600" data-state="closed" data-tippy-content="<span style='font-weight: bold; margin-bottom: 0;'><%= option.title %></span><br><%= number_to_currency(option.course_of_treatment&.total_price, :unit => "£") %>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-list h-4 w-4">
                              <rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect>
                              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                              <path d="M12 11h4"></path>
                              <path d="M12 16h4"></path>
                              <path d="M8 11h.01"></path>
                              <path d="M8 16h.01"></path>
                            </svg>
                          </a>
                        <% else %>
                          <a href="<%= option.pdf_file.url %>" target="_blank" class="flex items-center justify-center h-8 w-8 rounded-full bg-gray-100 text-gray-500" data-state="closed" data-tippy-content="<span style='font-weight: bold; margin-bottom: 0;'><%= option.title %></span><br><%= number_to_currency(option.course_of_treatment&.total_price, :unit => "£") %>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-list h-4 w-4">
                              <rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect>
                              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                              <path d="M12 11h4"></path>
                              <path d="M12 16h4"></path>
                              <path d="M8 11h.01"></path>
                              <path d="M8 16h.01"></path>
                            </svg>
                          </a>
                        <% end %>
                      <% end %>
                    <% end %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>

        <%= will_paginate @treatment_plans %>
      </div>
    </div>
  </main>
</div>
