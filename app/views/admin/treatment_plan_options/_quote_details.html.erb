<% ch_treatments = [] %>
<% teethOrder = [
  'UR8', 'UR7', 'UR6', 'UR5', 'UR4', 'UR3', 'UR2', 'UR1',
  'UL1', 'UL2', 'UL3', 'UL4', 'UL5', 'UL6', 'UL7', 'UL8',
  'LL8', 'LL7', 'LL6', 'LL5', 'LL4', 'LL3', 'LL2', 'LL1',
  'LR1', 'LR2', 'LR3', 'LR4', 'LR5', 'LR6', 'LR7', 'LR8'
] %>
<% base_chart = @option.patient.base_chart %>
<% completed_treatments = base_chart.charted_treatments.includes(:treatment).select { |ct| ct.completed_at.present? }.map do |ct|
  {
    id: ct.id,
    position: ct.position,
    treatment_name: (ct.treatment.treatment_folder || ct.treatment.patient_friendly_name).downcase,
    is_full_tooth_treatment: ct.is_full_tooth_treatment?,
    surface: (eval(ct.surface).keys.first rescue nil),
    treatment_id: ct.treatment_id,
    indicates_missing: ct.treatment.missing_tooth
  }
end %>
<% ch_treatments_source = @option.course_of_treatment.charting_appointments.map(&:charted_treatments).flatten %>
<% ChartedTreatment.includes(:treatment).where(id: ch_treatments_source.map(&:id)).map do |ct|
  json_str = ct.surface != nil ? ct.surface.gsub('=>', ':') : '{"": ""}'
  surface_hash = JSON.parse(json_str)

  position_parts = ct.position.split(' - ').map(&:strip)
  relevant_teeth =
    if position_parts.size == 2
      start_tooth, end_tooth = position_parts
      start_index = teethOrder.index(start_tooth)
      end_index   = teethOrder.index(end_tooth)

      if start_index && end_index
        if start_index > end_index
          start_index, end_index = end_index, start_index
        end
        teethOrder[start_index..end_index]
      else
        [ct.position]
      end
    else
      [ct.position]
    end

  surface_hash.each do |surface_name, _val|
    relevant_teeth.each do |single_tooth|
      ch_treatments << {
        position:         single_tooth,
        treatment_name:   (ct.treatment.treatment_folder || ct.treatment.patient_friendly_name).downcase,
        is_full_tooth_treatment: ct.is_full_tooth_treatment?,
        surface:          surface_name,
        treatment_id:     ct.treatment_id,
        id:               ct.id,
        indicates_missing: ct.treatment.missing_tooth
      }
    end
  end
end %>
<div style="height: 100%;" class="page-break">
  <div style="padding-right: 80px; padding-left: 80px;">
    <div style="padding-top: 120px;">
      <p class="page-header">Treatment Plan</p>
      <p class="page-header" style="font-weight: 275;"><%= @option.title %></p>
    </div>
    <div>
      <div style="padding-top: 50px;">
        <div class="teethgrid">
          <div style="display: -webkit-box; padding-bottom: 70px">
            <div class="ur8f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur8', 'UR8', 'Ur8', 'uR8'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR8F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur8', 'UR8', 'Ur8', 'uR8'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur7f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur7', 'UR7', 'Ur7', 'uR7'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR7F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur7', 'UR7', 'Ur7', 'uR7'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur6f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur6', 'UR6', 'Ur6', 'uR6'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR6F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur6', 'UR6', 'Ur6', 'uR6'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur5f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur5', 'UR5', 'Ur5', 'uR5'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR5F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur5', 'UR5', 'Ur5', 'uR5'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur4f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur4', 'UR4', 'Ur4', 'uR4'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR4F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur4', 'UR4', 'Ur4', 'uR4'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur3f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur3', 'UR3', 'Ur3', 'uR3'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR3F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur3', 'UR3', 'Ur3', 'uR3'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur2f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur2', 'UR2', 'Ur2', 'uR2'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR2F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur2', 'UR2', 'Ur2', 'uR2'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur1f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur1', 'UR1', 'Ur1', 'uR1'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR1F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur1', 'UR1', 'Ur1', 'uR1'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul1f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul1', 'UL1', 'Ul1', 'uL1'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL1F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul1', 'UL1', 'Ul1', 'uL1'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul2f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul2', 'UL2', 'Ul2', 'uL2'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL2F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul2', 'UL2', 'Ul2', 'uL2'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul3f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul3', 'UL3', 'Ul3', 'uL3'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL3F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul3', 'UL3', 'Ul3', 'uL3'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul4f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul4', 'UL4', 'Ul4', 'uL4'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL4F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul4', 'UL4', 'Ul4', 'uL4'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul5f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul5', 'UL5', 'Ul5', 'uL5'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL5F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul5', 'UL5', 'Ul5', 'uL5'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul6f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul6', 'UL6', 'Ul6', 'uL6'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL6F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul6', 'UL6', 'Ul6', 'uL6'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul7f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul7', 'UL7', 'Ul7', 'uL7'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL7F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul7', 'UL7', 'Ul7', 'uL7'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul8f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul8', 'UL8', 'Ul8', 'uL8'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL8F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul8', 'UL8', 'Ul8', 'uL8'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
          </div>
          <div style="display: -webkit-box;padding-bottom: 70px; padding-top:40px">
            <div class="ur8 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur8', 'UR8', 'Ur8', 'uR8'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR8.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur8', 'UR8', 'Ur8', 'uR8'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur7 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur7', 'UR7', 'Ur7', 'uR7'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR7.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur7', 'UR7', 'Ur7', 'uR7'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur6 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur6', 'UR6', 'Ur6', 'uR6'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR6.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur6', 'UR6', 'Ur6', 'uR6'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur5 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur5', 'UR5', 'Ur5', 'uR5'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR5.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur5', 'UR5', 'Ur5', 'uR5'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur4 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur4', 'UR4', 'Ur4', 'uR4'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR4.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur4', 'UR4', 'Ur4', 'uR4'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur3 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur3', 'UR3', 'Ur3', 'uR3'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR3.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur3', 'UR3', 'Ur3', 'uR3'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur2 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur2', 'UR2', 'Ur2', 'uR2'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR2.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur2', 'UR2', 'Ur2', 'uR2'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ur1 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ur1', 'UR1', 'Ur1', 'uR1'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UR1.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ur1', 'UR1', 'Ur1', 'uR1'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul1 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul1', 'UL1', 'Ul1', 'uL1'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL1.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul1', 'UL1', 'Ul1', 'uL1'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul2 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul2', 'UL2', 'Ul2', 'uL2'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL2.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul2', 'UL2', 'Ul2', 'uL2'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul3 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul3', 'UL3', 'Ul3', 'uL3'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL3.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul3', 'UL3', 'Ul3', 'uL3'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul4 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul4', 'UL4', 'Ul4', 'uL4'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL4.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul4', 'UL4', 'Ul4', 'uL4'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul5 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul5', 'UL5', 'Ul5', 'uL5'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL5.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul5', 'UL5', 'Ul5', 'uL5'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul6 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul6', 'UL6', 'Ul6', 'uL6'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL6.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul6', 'UL6', 'Ul6', 'uL6'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul7 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul7', 'UL7', 'Ul7', 'uL7'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL7.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul7', 'UL7', 'Ul7', 'uL7'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ul8 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ul8', 'UL8', 'Ul8', 'uL8'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/UL8.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ul8', 'UL8', 'Ul8', 'uL8'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
          </div>
          <div style="display: -webkit-box;padding-bottom: 50px">
            <div class="lr8 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr8', 'LR8', 'Lr8', 'lR8'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR8.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr8', 'LR8', 'Lr8', 'lR8'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr7 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr7', 'LR7', 'Lr7', 'lR7'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR7.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr7', 'LR7', 'Lr7', 'lR7'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr6 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr6', 'LR6', 'Lr6', 'lR6'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR6.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr6', 'LR6', 'Lr6', 'lR6'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr5 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr5', 'LR5', 'Lr5', 'lR5'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR5.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr5', 'LR5', 'Lr5', 'lR5'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr4 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr4', 'LR4', 'Lr4', 'lR4'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR4.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr4', 'LR4', 'Lr4', 'lR4'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr3 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr3', 'LR3', 'Lr3', 'lR3'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR3.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr3', 'LR3', 'Lr3', 'lR3'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr2 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr2', 'LR2', 'Lr2', 'lR2'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR2.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr2', 'LR2', 'Lr2', 'lR2'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr1 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr1', 'LR1', 'Lr1', 'lR1'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR1.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr1', 'LR1', 'Lr1', 'lR1'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll1 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll1', 'LL1', 'Ll1', 'lL1'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL1.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll1', 'LL1', 'Ll1', 'lL1'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll2 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll2', 'LL2', 'Ll2', 'lL2'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL2.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll2', 'LL2', 'Ll2', 'lL2'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll3 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll3', 'LL3', 'Ll3', 'lL3'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL3.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll3', 'LL3', 'Ll3', 'lL3'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll4 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll4', 'LL4', 'Ll4', 'lL4'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL4.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll4', 'LL4', 'Ll4', 'lL4'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll5 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll5', 'LL5', 'Ll5', 'lL5'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL5.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll5', 'LL5', 'Ll5', 'lL5'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll6 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll6', 'LL6', 'Ll6', 'lL6'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL6.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll6', 'LL6', 'Ll6', 'lL6'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll7 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll7', 'LL7', 'Ll7', 'lL7'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL7.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll7', 'LL7', 'Ll7', 'lL7'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll8 position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll8', 'LL8', 'Ll8', 'lL8'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL8.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll8', 'LL8', 'Ll8', 'lL8'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
          </div>
          <div style="display: -webkit-box;padding-bottom: 70px">
            <div class="lr8f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr8', 'LR8', 'Lr8', 'lR8'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR8F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr8', 'LR8', 'Lr8', 'lR8'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr7f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr7', 'LR7', 'Lr7', 'lR7'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR7F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr7', 'LR7', 'Lr7', 'lR7'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr6f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr6', 'LR6', 'Lr6', 'lR6'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR6F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr6', 'LR6', 'Lr6', 'lR6'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr5f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr5', 'LR5', 'Lr5', 'lR5'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR5F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr5', 'LR5', 'Lr5', 'lR5'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr4f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr4', 'LR4', 'Lr4', 'lR4'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR4F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr4', 'LR4', 'Lr4', 'lR4'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr3f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr3', 'LR3', 'Lr3', 'lR3'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR3F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr3', 'LR3', 'Lr3', 'lR3'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr2f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr2', 'LR2', 'Lr2', 'lR2'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR2F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr2', 'LR2', 'Lr2', 'lR2'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="lr1f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['lr1', 'LR1', 'Lr1', 'lR1'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LR1F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['lr1', 'LR1', 'Lr1', 'lR1'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll1f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll1', 'LL1', 'Ll1', 'lL1'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL1F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll1', 'LL1', 'Ll1', 'lL1'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll2f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll2', 'LL2', 'Ll2', 'lL2'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL2F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll2', 'LL2', 'Ll2', 'lL2'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll3f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll3', 'LL3', 'Ll3', 'lL3'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL3F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll3', 'LL3', 'Ll3', 'lL3'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll4f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll4', 'LL4', 'Ll4', 'lL4'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL4F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll4', 'LL4', 'Ll4', 'lL4'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll5f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll5', 'LL5', 'Ll5', 'lL5'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL5F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll5', 'LL5', 'Ll5', 'lL5'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll6f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll6', 'LL6', 'Ll6', 'lL6'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL6F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll6', 'LL6', 'Ll6', 'lL6'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll7f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll7', 'LL7', 'Ll7', 'lL7'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL7F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display:<%= completed_treatments.select { |ct| ['ll7', 'LL7', 'Ll7', 'lL7'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
            <div class="ll8f position-relative col p-0" style="width: 6.25%; position: relative">
              <% ch_treatments.select { |ct| ['ll8', 'LL8', 'Ll8', 'lL8'].include?(ct[:position]) }.each do |item| %>
                <img src='<%= "https://upodmedican.b-cdn.net/#{item[:treatment_name]}/#{item[:position]}F#{!item[:is_full_tooth_treatment] ? "_#{item[:surface]}" : ''}.png" %>' width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 999; display: <%= item[:indicates_missing] ? 'none' : 'block' %>">
              <% end %>
              <img src="https://upodmedican.b-cdn.net/Adult Teeth Individual Final/LL8F.png" width="100%;" height="auto" style="position: absolute; left: 0; top: 0; z-index: 10; display: <%= completed_treatments.select { |ct| ['ll8', 'LL8', 'Ll8', 'lL8'].include?(ct[:position]) }.any? { |item| item[:indicates_missing] } ? 'none' : 'block' %>">
            </div>
          </div>
        </div>
      </div>
      <div style="padding-top: 70px;">
        <p class="info-description">
          <%= @option.proposed_course_of_treatment_notes.html_safe || '' %>
        </p>
      </div>
    </div>
  </div>
</div>
