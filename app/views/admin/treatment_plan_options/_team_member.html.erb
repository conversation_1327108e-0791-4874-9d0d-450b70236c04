<div style="height: 100%;" class="page-break">
  <div style="padding-right: 80px; padding-left: 80px;">
    <div style="padding-top: 60px;">
      <p class="page-header" style="font-weight: 300; line-height: 90px">Meet your team</p>
      <p class="page-header" style="font-size: 90px; line-height: 90px"><%= member.full_name %></p>
      <p class="page-header" style="text-align: end; font-weight: 300; line-height: 90px"><%= member.roles.map(&:name).join(', ') %></p>
    </div>

    <div style="display: flex; flex-direction: column; align-items: center; margin: 20px 0 0 0;">
      <div style="margin: 0 auto;">
        <% if member.image.attached? %>
          <img width="auto" height="550px" style="display: block; margin: 0 auto; object-fit: cover;" src="<%= member.image.url %>">
        <% else %>
          <img src="<%= @request_base %>/images/default-avatar.jpg" style="display: block; margin: 0 auto; border-radius: 550px;object-fit: cover;" width="550px" height="550px">
        <% end %>
      </div>
      <hr style="border: 1px solid black; width: 100%;">
    </div>
    <div style="padding: 20px 0 0 0;">
      <p class="page-header" style="line-height: 80px">About</p>
      <p class="info-description" style="color: #000000;"><%= member.online_booking_description %></p>
    </div>
  </div>
</div>
