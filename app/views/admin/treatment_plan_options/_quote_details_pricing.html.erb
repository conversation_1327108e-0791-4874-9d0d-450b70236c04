<div style="height: 100%;" class="page-break">
  <div style="padding-right: 80px; padding-left: 80px;">
    <div style="padding-top: 100px;">
      <p class="page-header">Pricing</p>
    </div>
    <% @option.course_of_treatment.charting_appointments.sort_by(&:position).each_with_index do |treatmentPlanOptions, index| %>
      <div style="padding-top: 55px;">
        <div style="padding-bottom: 30px">
          <span style="font-family: 'Poppins', sans-serif; margin: 0; font-size: 22px; color: #303030; font-weight: 600; line-height: 30px;">Appointment <%= index + 1 %> </span>
        </div>
        <table style="padding-top: 20px; width: 100%; border-collapse: collapse">
          <thead>
          <tr>
            <th style="padding: 17px 12px; text-align: left; font-family: 'Poppins', sans-serif; font-weight: 600; width: 50%;">Treatment</th>
            <th style="padding: 17px 12px; text-align: left; font-family: 'Poppins', sans-serif; font-weight: 600; width: 35%;">Tooth
              Position
            </th>
            <th style="padding: 17px 12px; text-align: left; font-family: 'Poppins', sans-serif; font-weight: 600; width: 15%;">Cost</th>
          </tr>
          </thead>
          <tbody>
          <% treatmentPlanOptions.charted_treatments.each do |tr| %>
            <% price = @option.charted_treatment_prices.dig(tr.id.to_s, 'override_price') || tr.price %>
            <tr style="background-color: #ffffff; border:1px solid #ffffff; border-radius:20px">
              <td style=";padding: 17px 12px; text-align: left; font-family: 'Poppins', sans-serif; font-weight: 400;"><%= tr.treatment.patient_friendly_name %></td>
              <td style="padding: 17px 12px; text-align: left; font-family: 'Poppins', sans-serif; font-weight: 400;"><%= tr.position %></td>
              <td style="padding: 17px 12px; text-align: left; font-family: 'Poppins', sans-serif; font-weight: 400;"><%= number_to_currency(price.to_f, unit: "£", delimiter: '') %></td>
            </tr>
          <% end %>
          </tbody>
        </table>
      </div>
    <% end %>
<!--          <div style="text-align: right; padding-top: 55px;">-->
    <%# @option.course_of_treatment.charting_appointments.each_with_index do |treatmentPlanOptions, index| %>
    <%# total_sum += treatmentPlanOptions.charted_treatments.map(&:override_price).reject(&:blank?).sum %>
    <%# end %>
<!--            <span style="background-color: #ffffff; border:1px solid #ffffff; border-radius:20px; padding-left: 12px; padding-top: 17px; padding-bottom: 17px; padding-right: 200px; text-align: left; font-family: 'Poppins', sans-serif; font-weight: 400;">Subtotal</span>-->
<!--            <span style="background-color: #ffffff; border:1px solid #ffffff; border-radius:20px; padding: 17px 62px 17px 12px; text-align: left; font-family: 'Poppins', sans-serif; font-weight: 400;"><%#= number_to_currency(total_sum, unit: "£", delimiter: '')%></span>-->
<!--          </div>-->
    <div style="text-align: right; padding-top: 55px;">
      <% total_discount = 0 %>
      <%# @option.course_of_treatment.charting_appointments.each_with_index do |treatmentPlanOptions, index| %>
      <%# total_discount += treatmentPlanOptions.charted_treatments.map(&:discount_amount).reject(&:blank?).sum %>
      <%# end %>
      <%# total_discounted_sum = total_sum - total_discount %>
      <% @option.course_of_treatment.charting_appointments&.each do |charting_appt|
        charting_appt.charted_treatments.each do |ct|
          total_discount += @option.charted_treatment_prices.dig(ct.id.to_s, 'override_price').to_f || ct.price.to_f
        end
      end %>
      <span style="background-color: #ffffff; border:1px solid #ffffff; border-radius:20px;padding-left: 12px; padding-top: 17px; padding-bottom: 17px; padding-right: 200px; text-align: left; font-family: 'Poppins', sans-serif; font-weight: 400;">Total</span>
      <span style="background-color: #ffffff; border:1px solid #ffffff; border-radius:20px; padding: 17px 62px 17px 12px; text-align: left; font-family: 'Poppins', sans-serif; font-weight: 400;"><%= number_to_currency(total_discount, unit: "£", delimiter: '') %></span>
    </div>
  </div>
</div>
