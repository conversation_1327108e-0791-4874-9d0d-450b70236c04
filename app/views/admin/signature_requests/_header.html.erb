<div class="flex items-center justify-between mb-6">
  <div class="flex items-center">
    <h1 class="text-2xl font-medium">
      <span class="text-gray-800">Secure Send</span>
    </h1>
    <button type="button" data-practitioner-id="<%= current_user.id %>" class="ml-4 flex items-center px-4 py-1.5 rounded-full text-sm font-medium transition-all border bg-gradient-to-b
      from-white to-gray-50 shadow-md hover:shadow-lg border-gray-200 text-gray-800 cursor-pointer focus-on-me-btn" aria-pressed="false">
      <div class="w-6 h-6 rounded-full overflow-hidden mr-2 flex-shrink-0 flex items-center justify-center shadow-sm border border-gray-300 bg-gray-50">
        <%= image_tag current_user.image.attached? ? current_user.image : "default-avatar.webp", class: "w-full h-full object-cover" %>
      </div>
      <span>Focus on me</span>
    </button>
  </div>
  <div class="flex items-center space-x-3">
    <button type="button" data-action="open-filter" class="cursor-pointer px-4 py-2 text-sm rounded-full bg-white shadow-sm flex items-center relative" data-filter-button="true">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter w-4 h-4 mr-2">
        <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
      </svg>
      <span>Filters</span>
      <span id="active-filter-count" class="ml-1 w-4 h-4 rounded-full bg-blue-500 text-white text-[10px] flex items-center justify-center hidden">0</span>
    </button>
    <%= link_to new_admin_signature_request_path,
                class: 'px-4 py-2 text-sm rounded-full bg-blue-200 text-blue-700 shadow-sm flex items-center hover:bg-blue-300 transition-colors border border-blue-300',
                style: 'text-decoration: none;' do %>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-4 h-4 mr-1">
        <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
        <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
        <path d="M10 9H8"></path>
        <path d="M16 13H8"></path>
        <path d="M16 17H8"></path>
      </svg>
      Send New Document
    <% end %>
  </div>
</div>

<%= render 'admin/signature_requests/filter_modal' %>
