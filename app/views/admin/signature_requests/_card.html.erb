<% patient = signature_request.patient %>
<% signable_document = signature_request.signable_document %>
<% doc_type = signable_document.document_template&.template_type&.humanize || 'Treatment plan' %>
<% doc_type_color = document_type_color(doc_type) %>
<% sent_by = signature_request.sent_by %>
<% progress = due_date_progress(signature_request) %>
<div tabindex="0"
     data-source-title="<%= title %>"
     class="request-card cursor-grab bg-white rounded-xl p-4 pb-3 mb-3 relative transition-all shadow-md hover:shadow-lg"
     data-request-id="<%= signature_request.id %>"
     data-practitioner-id="<%= signature_request.sent_by_id %>">
  <div class="member hidden"><%= signature_request.sent_by_id %></div>

  <div class="flex justify-between items-start">
    <div class="flex-1">
      <div class="mb-2"></div>
      <div class="text-[15px] font-medium flex items-center justify-between">
        <div class="flex items-center flex-grow">
          <%= link_to patient.full_name, admin_patient_url(patient.id), class: 'patient-name hover:text-blue-600 transition-colors text-left mr-2' %>
          <%= link_to conversation_admin_patient_url(patient.id),
                      class: 'relative flex items-center justify-center w-7 h-7 rounded-full bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 transition-all hover:scale-110 group shadow-sm border border-blue-100' do %>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square w-3.5 h-3.5">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
          <% end %>
        </div>
      </div>

      <div class="mt-1 flex items-center">
        <div class="w-2 h-8 rounded-l-md bg-<%= doc_type_color %>-200"></div>
        <div class="flex items-center px-3 py-1.5 rounded-r-md border-y border-r border-<%= doc_type_color %>-200 bg-<%= doc_type_color %>-50 text-<%= doc_type_color %>-700 flex-1">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-3.5 h-3.5 mr-2 text-<%= doc_type_color %>-500">
            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
            <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
            <path d="M10 9H8"></path>
            <path d="M16 13H8"></path>
            <path d="M16 17H8"></path>
          </svg>
          <span class="text-[13px] font-medium"><%= doc_type %></span>
        </div>
      </div>
    </div>
    <button class="p-1 rounded-full hover:bg-gray-100 text-gray-500 hover:text-gray-700 cursor-pointer sr-actions-toggle"
            data-dropdown-toggle="request-dropdown-<%= signature_request.id %>" data-no-drag>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
           stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis w-4 h-4">
        <circle cx="12" cy="12" r="1"></circle>
        <circle cx="19" cy="12" r="1"></circle>
        <circle cx="5" cy="12" r="1"></circle>
      </svg>
    </button>

    <div id="request-dropdown-<%= signature_request.id %>" class="absolute right-2 top-8 bg-white rounded-lg shadow-lg z-30 w-55 overflow-hidden sr-actions-menu hidden" data-no-drag>
      <% if signable_document.letter.present? %>
        <% if signable_document.letter.stannp_pdf_url %>
          <div class="py-1">
            <button class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 cursor-pointer">
              <a href="<%= signable_document.letter.stannp_pdf_url %>" target="_blank">Letter PDF</a>
            </button>
          </div>
        <% end %>
      <% else %>
        <div class="py-1">
          <button class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 cursor-pointer">
            <a href="<%= admin_signature_request_path(signature_request) %>" target="_blank">View document</a>
          </button>
        </div>

        <% unless signature_request.signed? %>
          <div class="py-1">
            <button
              type="button"
              class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 cursor-pointer"
              data-doc-id="<%= signature_request.id %>"
              data-url="<%= patients_signature_request_url(signature_request) %>"
              data-person-modal-target="signInPersonModal"
            >
              Sign in Person
            </button>
          </div>

          <div class="py-1">
            <button type="button" class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 cursor-pointer">
              <%= link_to "Resend for signing via Email", resend_via_email_admin_signature_request_path(signature_request), method: :post %>
            </button>
          </div>

          <div class="py-1">
            <button type="button" class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 cursor-pointer">
              <%= link_to "Resend for signing via SMS", resend_via_sms_admin_signature_request_path(signature_request), method: :post %>
            </button>
          </div>

          <div class="py-1">
            <button type="button" class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 cursor-pointer">
              <%= link_to "Edit Document", edit_admin_signature_request_path(signature_request) %>
            </button>
          </div>
        <% end %>

        <div class="py-1 border-t border-gray-100">
          <button type="button" class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 text-red-600 cursor-pointer">
            <%= link_to "Archive Document", admin_signature_request_path(signature_request, signature_request: { status: 'archived' }), method: :patch %>
          </button>
        </div>
      <% end %>
    </div>
  </div>

  <div class="flex flex-col space-y-1 text-[13px] text-gray-600 mt-3">
    <div class="flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
           stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail w-3.5 h-3.5 mr-2 text-gray-400">
        <rect width="20" height="16" x="2" y="4" rx="2"></rect>
        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
      </svg>
      <span><%= patient.email %></span>
    </div>
    <div class="flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
           stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone w-3.5 h-3.5 mr-2 text-gray-400">
        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
      </svg>
      <span><%= patient.mobile_phone %></span>
    </div>
  </div>

  <div class="mt-3 pt-2 border-t border-gray-100">
    <div class="flex items-center mb-1.5">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-3.5 h-3.5 text-gray-500 mr-1.5">
        <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
        <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
        <path d="M10 9H8"></path>
        <path d="M16 13H8"></path>
        <path d="M16 17H8"></path>
      </svg>
      <span class="text-[12px] font-medium text-gray-700">Documents Sent</span>
    </div>
    <div class="flex items-center bg-<%= color %>-50 border-<%= color %>-100 rounded-md px-2.5 py-1.5 border mb-2">
      <div class="w-3.5 h-3.5 rounded-sm bg-<%= color %>-100 flex items-center justify-center mr-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-2 h-2 text-<%= color %>-600">
          <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
          <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
          <path d="M10 9H8"></path>
          <path d="M16 13H8"></path>
          <path d="M16 17H8"></path>
        </svg>
      </div>
      <span class="text-[12px] text-<%= color %>-700 flex-1 truncate"><%= signable_document.document_template&.name || signable_document.treatment_plan_option&.title %></span>
    </div>
  </div>

  <div class="mt-3 pt-2 border-t border-gray-100">
    <div class="flex items-center mb-1.5">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-3.5 h-3.5 text-gray-500 mr-1.5">
        <path d="M8 2v4"></path>
        <path d="M16 2v4"></path>
        <rect width="18" height="18" x="3" y="4" rx="2"></rect>
        <path d="M3 10h18"></path>
      </svg>
      <span class="text-[12px] font-medium text-gray-700">Linked Appointment</span>
    </div>
    <div class="flex items-center bg-purple-50 rounded-md px-2.5 py-1.5 border border-purple-100 mb-2">
      <div class="w-3.5 h-3.5 rounded-sm bg-purple-100 flex items-center justify-center mr-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-2 h-2 text-purple-600">
          <path d="M8 2v4"></path>
          <path d="M16 2v4"></path>
          <rect width="18" height="18" x="3" y="4" rx="2"></rect>
          <path d="M3 10h18"></path>
        </svg>
      </div>
      <%= formatted_calendar_booking(signature_request) %>
    </div>
  </div>

  <div class="mt-2 pt-2 border-t border-gray-100">
    <div class="flex items-center justify-between">
      <span class="text-[12px] text-gray-600">Sent by</span>
      <div class="relative">
        <div class="w-7 h-7 rounded-full bg-gray-200 border border-[#d1d1d6] overflow-hidden flex-shrink-0 shadow-sm">
          <%= image_tag sent_by&.image&.attached? ? sent_by&.image&.url : "default-avatar.webp", class: "w-full h-full object-cover", data: { "tippy-content": sent_by&.full_name || 'System' }  %>
        </div>
      </div>
    </div>
  </div>

  <div class="mt-3 pt-2 border-t border-gray-100">
    <div class="flex items-center mb-1.5">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-3.5 h-3.5 text-gray-500 mr-1.5">
        <path d="M8 2v4"></path>
        <path d="M16 2v4"></path>
        <rect width="18" height="18" x="3" y="4" rx="2"></rect>
        <path d="M3 10h18"></path>
      </svg>
      <span class="text-[12px] font-medium text-gray-700">Due Date</span>
    </div>
    <div class="flex flex-col space-y-1.5 <%= 'bg-red-50 p-3 -mx-4 border border-red-200' if title == 'Late' %>">
      <div class="flex items-center justify-between">
        <% if title == 'Signed - Awaiting Staff Check' %>
          <div class="text-[13px] font-medium text-<%= color %>-500 w-full text-center">Signed</div>
        <% elsif title == 'Late' %>
          <div class="text-[13px] font-medium text-<%= color %>-500 w-full text-center"><%= title %></div>
        <% elsif signature_request.due_by.present? %>
          <span class="text-[13px] font-medium text-gray-800"><%= signature_request.due_by.strftime('%d/%m/%Y') %></span>
          <span class="text-[11px] text-gray-500"><%= progress[:days_left_text] %></span>
        <% else %>
          <div class="text-[13px] font-medium text-gray-800 w-full text-center">No Due Date</div>
        <% end %>
      </div>

      <div class="h-1.5 w-full bg-gray-100 rounded-full overflow-hidden">
        <% if title == 'Signed - Awaiting Staff Check' || title == 'Late' || signature_request.due_by.blank? %>
          <div class="h-full bg-<%= color %>-500 rounded-full <%= 'animate-pulse' if title == 'Late' %>" style="width: 100%;"></div>
        <% else %>
          <div class="h-full rounded-full bg-<%= color %>-400" style="width: <%= progress[:percentage] %>%;"></div>
        <% end %>
      </div>
    </div>
  </div>

  <div class="mt-3 pt-2 border-t border-gray-100">
    <div class="bg-gradient-to-r from-blue-50 to-gray-50 rounded-lg px-3 py-2.5 flex items-center justify-between min-w-0 shadow-sm hover:shadow transition-all duration-200">
      <div class="flex items-center">
        <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-2 flex-shrink-0 shadow-sm border border-blue-200">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-3.5 h-3.5 text-blue-600">
            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
            <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
            <path d="M10 9H8"></path>
            <path d="M16 13H8"></path>
            <path d="M16 17H8"></path>
          </svg>
        </div>
        <span class="text-[13px] font-medium text-blue-700 whitespace-nowrap"></span>
      </div>
      <div class="flex items-center gap-2">
        <span class="text-[13px] font-medium text-gray-700 whitespace-nowrap"><%= signature_request.created_at.strftime('%d/%m/%Y') %></span>
      </div>
    </div>
  </div>
</div>
