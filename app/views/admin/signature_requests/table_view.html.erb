<div class="h-[calc(100vh-56px)] bg-[#f5f5f7] text-[#1d1d1f] font-sf-pro">
  <main class="w-full h-full overflow-hidden">
    <div class="w-full px-4 sm:px-6 lg:px-8 py-12">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 tracking-tight">Secure Send</h1>
        <p class="mt-2 text-base text-gray-600">A summary of all <%= @status %> documents.</p>
      </div>

      <div class="relative w-full overflow-auto">
        <table class="w-full caption-bottom text-sm border-separate border-spacing-y-2">
          <thead class="[&amp;_tr]:border-b">
          <tr class="border-b transition-colors data-[state=selected]:bg-muted hover:bg-transparent">
            <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
            <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Document Type</th>
            <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Document Sent</th>
            <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Due</th>
            <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent By</th>
            <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
          </tr>
          </thead>

          <tbody class="[&_tr:last-child]:border-0">
          <% @signature_requests.each do |signature_request| %>
            <% patient = signature_request.patient %>
            <% signable_document = signature_request.signable_document %>
            <% doc_type = signable_document.document_template&.template_type&.humanize || 'CoT Estimate' %>
            <% sent_by = signature_request.sent_by %>
            <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted bg-white rounded-lg shadow-sm">
              <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap rounded-l-lg align-top">
                <div class="flex items-start">
                    <span class="relative flex shrink-0 overflow-hidden rounded-full h-10 w-10">
                      <% if patient.image.attached? %>
                        <%= image_tag(patient.image, class: "w-full h-full object-cover") %>
                      <% else %>
                        <div class="w-full h-full rounded-full bg-purple-800 text-white flex items-center justify-center text-sm font-medium border-2 border-white">
                          <%= patient.first_name.first %><%= patient.last_name.first %>
                        </div>
                      <% end %>
                    </span>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      <%= link_to patient.full_name, admin_patient_url(patient.id), class: 'hover:text-blue-600 transition-colors' %>
                    </div>
                    <div class="text-sm text-gray-500"><%= patient.email %></div>
                    <div class="flex items-center text-sm text-gray-500 mt-2 space-x-4">
                      <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4 mr-1.5 text-gray-400">
                          <path d="M8 2v4"></path>
                          <path d="M16 2v4"></path>
                          <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                          <path d="M3 10h18"></path>
                        </svg>
                        <span><%= patient.date_of_birth&.strftime("%d %b %Y") %></span>
                      </div>
                      <span class="bg-gray-100 text-gray-700 px-2 py-0.5 rounded-md text-xs font-medium"><%= age_in_years_and_months(patient.date_of_birth) %></span>
                    </div>
                  </div>
                </div>
              </td>
              <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap align-top">
                <% color = %w[green amber blue purple cyan].sample %>
                <div class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-<%= color %>-50 text-<%= color %>-700 border-<%= color %>-200/50 border font-semibold">
                  <%= doc_type %>
                </div>
              </td>
              <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap text-sm text-gray-700 align-top"><%= signable_document.document_template&.name || 'CoT Estimate' %></td>
              <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap text-sm text-gray-700 align-top"><%= signature_request.due_by&.strftime('%d/%m/%Y') || 'No Due Date' %></td>
              <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap align-top">
                <div class="flex items-center">
                  <% if sent_by.image.attached? %>
                    <%= image_tag(sent_by.image, class: "w-8 h-8 rounded-full mr-2", data: {tippy_content: sent_by.full_name }) %>
                  <% else %>
                    <div class="w-8 h-8 rounded-full bg-purple-800 text-white flex items-center justify-center text-sm font-medium border-2 border-white mr-2" data-tippy-content="<%= sent_by.full_name %>">
                      <%= sent_by.first_name.first %><%= sent_by.last_name.first %>
                    </div>
                  <% end %>
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900"><%= sent_by.full_name %></div>
                  </div>
                </div>
              </td>
              <td class="align-top py-4 whitespace-nowrap">
                <div class="flex items-center gap-2 group">
                  <a href="<%= admin_signature_request_path(signature_request) %>" target="_blank" class="inline-flex flex-row items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 rounded-md text-xs h-auto px-2.5 py-1.5 duration-200 text-gray-500 hover:text-gray-700 bg-gray-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye h-3.5 w-3.5">
                      <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                    View
                  </a>
                </div>
              </td>
            </tr>
          <% end %>
          </tbody>
        </table>

        <%= will_paginate @signature_requests %>
      </div>
    </div>
  </main>
</div>
