<div class="lab-works-table-view h-[calc(100vh-56px)] bg-[#f5f5f7] text-[#1d1d1f] font-sf-pro">
  <main class="w-full h-full overflow-hidden">
    <div class="w-full h-full px-4 sm:px-6 lg:px-8 py-12">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 tracking-tight">Lab Works</h1>
        <p class="mt-2 text-base text-gray-600">A summary of all <%= @status == 'Fitted Successfully - Auto Archive' ? 'completed' : 'archived' %> patient lab works.</p>
      </div>

      <div class="relative w-full h-full overflow-auto">
        <table class="w-full caption-bottom text-sm border-separate border-spacing-y-2">
          <thead class="[&amp;_tr]:border-b">
            <tr class="border-b transition-colors data-[state=selected]:bg-muted hover:bg-transparent">
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Practice</th>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient Name</th>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lab Work</th>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lab</th>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent</th>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dentist</th>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Turnaround Time</th>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Linked Appointment</th>
              <th class="h-12 align-middle [&amp;:has([role=checkbox])]:pr-0 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice Uploaded</th>
              <th class="h-12 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 px-6 py-3"></th>
            </tr>
          </thead>

          <tbody class="[&_tr:last-child]:border-0">
            <% statuses = lab_work_statuses %>
            <% @lab_works.each do |lab_work| %>
              <% practice = lab_work.practice %>
              <% lab = lab_work.lab %>
              <% practitioner = lab_work.practitioner %>
              <% ld = lab_work.lab_dockets&.last %>
              <% calendar_booking = lab_work.charting_appointment&.calendar_booking %>
              <% names = ld&.lab_docket_items&.map do |ldi|
                first_item = ldi.lab_items.first
                "#{first_item.treatment_type} - #{first_item.name}" if first_item.present?
              end&.compact %>
              <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted bg-white rounded-lg shadow-sm">
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 py-3 font-normal">
                  <div class="flex items-center gap-3 bg-background">
                    <span class="relative flex shrink-0 h-8 w-8 rounded-full overflow-hidden ring-1 ring-gray-200 shadow-sm">
                      <% if practice.logo.attached? %>
                        <%= image_tag(practice.logo, class: "w-full h-full object-cover") %>
                      <% else %>
                        <div class="w-full h-full rounded-full bg-purple-800 text-white flex items-center justify-center text-sm font-medium border-2 border-white">
                          <%= practice.name.first.upcase %>
                        </div>
                      <% end %>
                    </span>
                    <span class="font-medium text-gray-800"><%= practice.name %></span>
                  </div>
                </td>
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 py-3 font-normal">
                  <%= link_to lab_work.patient.full_name, admin_lab_work_url(lab_work.id), class: 'hover:text-blue-600 transition-colors' %>
                </td>
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 py-3 font-normal">
                  <div class="flex flex-wrap items-center gap-2 text-[12px] text-gray-500 mt-1 mb-1">
                    <% if names.present? %>
                      <% names.each do |name| %>
                        <% color = ['sky', 'emerald', 'pink', 'amber', 'teal', 'violet'].sample %>
                        <div class="inline-flex items-center rounded-full border transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 px-2 py-1 text-xs font-medium bg-<%= color %>-100 text-<%= color %>-700 border-<%= color %>-200">
                          <%= name %>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                </td>
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 py-3 font-normal">
                  <div class="flex items-center gap-3 bg-background">
                    <span class="relative flex shrink-0 h-8 w-8 rounded-full overflow-hidden ring-1 ring-gray-200 shadow-sm">
                      <% if lab.image.attached? %>
                        <%= image_tag(lab.image, class: "w-full h-full object-cover") %>
                      <% else %>
                        <div class="w-full h-full rounded-full bg-purple-800 text-white flex items-center justify-center text-sm font-medium border-2 border-white">
                          <%= lab.name.first.upcase %>
                        </div>
                      <% end %>
                    </span>
                    <span class="text-gray-800"><%= lab.name %></span>
                  </div>
                </td>
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 py-3 font-normal">
                  <%= lab_work.created_at.strftime("#{lab_work.created_at.day.ordinalize} %B %Y") %>
                </td>
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 py-3 font-normal">
                  <div class="flex items-center gap-3 bg-background">
                    <span class="relative flex shrink-0 h-8 w-8 rounded-full overflow-hidden ring-1 ring-gray-200 shadow-sm">
                      <% if practitioner.image.attached? %>
                        <%= image_tag(practitioner.image, class: "w-full h-full rounded-full mr-2") %>
                      <% else %>
                        <%= image_tag('default-avatar.webp', class: "w-full h-full rounded-full mr-2") %>
                      <% end %>
                    </span>
                    <span class=" text-gray-800"><%= practitioner.full_name %></span>
                  </div>
                </td>
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 py-3 font-normal">
                  <% if ld.present? %>
                    <%= ld.total_days + 1 %> <%= ld.total_days == 0 ? 'Day' : 'Days' %>
                  <% else %>
                    N/A
                  <% end %>

                </td>

                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 py-3 font-normal">
                  <% if calendar_booking.present? %>
                    <div class="flex items-center gap-2">
                      <div class="bg-indigo-100 p-1.5 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-3.5 w-3.5 text-indigo-500">
                          <path d="M8 2v4"></path>
                          <path d="M16 2v4"></path>
                          <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                          <path d="M3 10h18"></path>
                        </svg>
                      </div>
                      <div class="flex flex-col">
                        <span class="font-medium text-gray-800"><%= calendar_booking.start_time.strftime('%-d %B %Y') %></span>
                        <div class="flex items-center gap-1 text-xs text-gray-500">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-3 w-3">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                          </svg>
                          <span><%= calendar_booking.start_time.strftime('%H:%M') %></span>
                        </div>
                      </div>
                    </div>
                  <% else %>
                    <div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 px-2 py-1 text-xs bg-slate-100 text-gray-600 border-gray-200 w-fit">
                      Not booked
                    </div>
                  <% end %>
                </td>

                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 py-3 font-normal">
                  <% if lab_work.invoice.attached? %>
                    Yes
                  <% else %>
                    No
                  <% end %>
                </td>

                <td class="p-4 [&amp;:has([role=checkbox])]:pr-0 px-6 py-4 whitespace-nowrap text-right text-sm font-medium rounded-r-lg align-top">
                  <div class="relative">
                    <button onclick="toggleSettingsDropdown('dropdown-<%= lab_work.id %>')" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8" type="button">
                      <span class="material-symbols-outlined text-slate-500 group-hover:text-slate-700">more_horiz</span>
                      <span class="sr-only">Actions</span>
                    </button>

                    <div id="dropdown-<%= lab_work.id %>" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                      <div class="py-1">
                        <%= link_to admin_lab_work_url(lab_work.id), class: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" do %>
                          View Lab Work
                        <% end %>
                        <button type="button"
                                class="update-status-btn block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                data-lab-work-id="<%= lab_work.id %>"
                                data-statuses='<%= raw statuses.to_json %>'
                                data-url="<%= update_status_admin_lab_work_path(lab_work, format: :json) %>">
                          Update Status
                        </button>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>

        <%= will_paginate @lab_works %>
      </div>
    </div>
  </main>
</div>
