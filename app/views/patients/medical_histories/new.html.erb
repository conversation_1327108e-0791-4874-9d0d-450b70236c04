<div class="flex h-screen">
  <%= render 'patients/patients/aside', selected: 'documents' %>

  <%= form_with model: @medical_history,
                url: patients_medical_histories_path,
                local: true,
                class: 'flex-1 overflow-y-auto',
                id: "customer-medical-form" do |f| %>
    <div class="min-h-screen bg-slate-100 flex flex-col items-center justify-start py-4 px-4 sm:px-6 lg:px-8">
      <div class="w-full max-w-[50.4rem] space-y-4">
        <div class="text-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart-pulse mx-auto h-10 w-10 text-sky-500 mb-3">
            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path>
            <path d="M3.22 12H9.5l.5-1 2 4.5 2-7 1.5 3.5h5.27"></path>
          </svg>
          <h1 class="text-2xl font-semibold text-slate-800">Update Your Medical History</h1>
          <p class="mt-1 text-sm text-slate-500">Please provide accurate information to help us offer you the best care.</p>
        </div>

        <div class="w-full pt-2">
          <div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100"
               class="relative overflow-hidden rounded-full bg-secondary w-full h-2 [&amp;&gt;*]:bg-sky-300">
            <div class="half-width-bar h-full flex-1 bg-primary transition-all"></div>
          </div>
          <p class="text-xs text-slate-500 mt-1 text-right">Page <span class="current-step">1</span> of <%= (@questions.count / 5.0).ceil %></p>
        </div>

        <div class="border bg-white text-card-foreground shadow-xl border-slate-200/60 rounded-2xl overflow-hidden">
          <div id="questions-section flex justify-center items-center col-md-6">
            <% @questions.each_slice(5).with_index do |questions_slice, page_index| %>
              <div class="question-page <%= 'hidden' if page_index > 0 %>" data-page="<%= page_index %>">
                <% questions_slice.each_with_index do |question, index| %>
                  <div class="question question-step p-4 space-y-4" data-step="<%= page_index * 5 + index %>" id="question-<%= question.id %>">
                    <p class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 block text-sm font-medium text-slate-700 mb-1">
                      <%= (page_index * 5 + index + 1) %>: <%= question.question %>
                    </p>

                    <% if question.multiple_answers? || question.single_answer? %>
                      <p class="text-sm text-slate-500 mb-2"><%= question.multiple_answers? ? "Select Multiple" : "Select One" %></p>
                      <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3">
                        <div class="my-4 flex-grow-1 req-r" data-toggle="buttons">
                          <% question.medical_history_answers.each do |ans| %>
                            <div class="custom-checkbox col-12 col-md-8 my-2">
                              <% if question.multiple_answers? %>
                                <%= check_box_tag "responses[#{question.id}][answers][]",
                                                  ans.id,
                                                  false,
                                                  class: "form-check-input option-select",
                                                  data: { question_id: question.id, answer_id: ans.id },
                                                  id: "q#{question.id}_a#{ans.id}" %>
                                <%= label_tag "q#{question.id}_a#{ans.id}",
                                              ans.answer,
                                              class: "form-check-label w-100 btn btn-outline-secondary text-start" %>
                              <% else %>
                                <div class="flex flex-1 items-center space-x-2 p-2 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors">
                                  <%= radio_button_tag "responses[#{question.id}][answers][]",
                                                       ans.id,
                                                       false,
                                                       class: "option-select aspect-square h-4 w-4 rounded-full border ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-sky-600 border-slate-400",
                                                       data: { question_id: question.id, answer_id: ans.id },
                                                       id: "q#{question.id}_a#{ans.id}" %>
                                  <%= label_tag "q#{question.id}_a#{ans.id}",
                                                ans.answer,
                                                class: "peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm text-slate-700 font-normal cursor-pointer",
                                                style: 'margin: 0;' %>
                                </div>
                              <% end %>
                            </div>

                            <%= render partial: "followup",
                                       locals: { parent_question: question, parent_answer: ans } %>
                          <% end %>
                        </div>
                      </div>

                    <% elsif question.text? %>
                      <% question.medical_history_answers.each do |ans| %>
                        <div class="my-4">
                          <%= text_field_tag "responses[#{question.id}][answers][#{ans.id}]",
                                             "",
                                             placeholder: ans.answer,
                                             class: "form-control req-text mb-3" %>
                        </div>
                      <% end %>

                    <% elsif question.medication_select? %>
                      <% ans = question.medical_history_answers.first %>
                      <div class="my-4 flex-grow-1">
                        <%= select_tag "responses[#{question.id}][answers][]",
                                       options_from_collection_for_select(Medication.all, "id", "name"),
                                       class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm mh-s2",
                                       multiple: true %>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>

          <div class="flex justify-between items-center p-4 border-t border-slate-200/60 mt-4">
            <button type="button" class="prev-btn inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border bg-background hover:text-accent-foreground h-10 px-4 py-2 text-slate-700 border-slate-300 hover:bg-slate-100">Back</button>
            <%= f.submit "Submit Questionnaire", class: "review-btn hidden items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 bg-sky-300 hover:bg-sky-400 text-sky-800" %>
            <button type="button" class="next-btn inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 bg-sky-300 hover:bg-sky-400 text-sky-800">Next</button>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <%= render 'patients/medical_histories/information_modal' %>
</div>
