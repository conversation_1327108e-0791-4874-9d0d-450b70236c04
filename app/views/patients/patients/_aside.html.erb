<div class="w-[80px] flex flex-col items-center bg-white/80 backdrop-blur-md border-r border-slate-200/70 py-6 space-y-6">
  <div class="mb-10">
    <%= image_tag((@practice.present? && @practice.logo.attached?) ? @practice.logo : "Logotype.svg", class: "object-cover w-[55px] h-[40px]") %>
  </div>

  <div class="flex flex-col items-center space-y-3 flex-1">
    <a href="<%= dashboard_patients_patients_path %>"
       class="p-3 rounded-xl transition-colors <%= selected == 'dashboard' ? 'text-sky-600 hover:text-slate-700 bg-sky-100 hover:bg-sky-100/90' : 'hover:bg-slate-200/60 text-slate-400 hover:text-slate-500' %>"
       title="Dashboard">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-grid h-5 w-5">
        <rect width="7" height="7" x="3" y="3" rx="1"></rect>
        <rect width="7" height="7" x="14" y="3" rx="1"></rect>
        <rect width="7" height="7" x="14" y="14" rx="1"></rect>
        <rect width="7" height="7" x="3" y="14" rx="1"></rect>
      </svg>
      <span class="sr-only">Dashboard</span>
    </a>

    <a href="<%= edit_patients_patient_path(current_patient) %>"
       class="p-3 rounded-xl transition-colors <%= selected == 'patient' ? 'text-sky-600 hover:text-slate-700 bg-sky-100 hover:bg-sky-100/90' : 'hover:bg-slate-200/60 text-slate-400 hover:text-slate-500' %>"
       title="Patient Details">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
           viewBox="0 0 24 24" fill="none" stroke="currentColor"
           stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
           class="lucide lucide-user h-5 w-5">
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
        <circle cx="12" cy="7" r="4"/>
      </svg>
      <span class="sr-only">Personal Details</span>
    </a>

    <a href="#"
       class="p-3 rounded-xl transition-colors <%= selected == 'appointments' ? 'text-sky-600 hover:text-slate-700 bg-sky-100 hover:bg-sky-100/90' : 'hover:bg-slate-200/60 text-slate-400 hover:text-slate-500' %>"
       title="Appointments">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar-days h-5 w-5">
        <path d="M8 2v4"></path>
        <path d="M16 2v4"></path>
        <rect width="18" height="18" x="3" y="4" rx="2"></rect>
        <path d="M3 10h18"></path>
        <path d="M8 14h.01"></path>
        <path d="M12 14h.01"></path>
        <path d="M16 14h.01"></path>
        <path d="M8 18h.01"></path>
        <path d="M12 18h.01"></path>
        <path d="M16 18h.01"></path>
      </svg>
      <span class="sr-only">Appointments</span>
    </a>

    <a href="#"
       class="p-3 rounded-xl transition-colors <%= selected == 'messages' ? 'text-sky-600 hover:text-slate-700 bg-sky-100 hover:bg-sky-100/90' : 'hover:bg-slate-200/60 text-slate-400 hover:text-slate-500' %>"
       title="Messages">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-5 w-5">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
      </svg>
      <span class="sr-only">Messages</span>
    </a>

    <a href="<%= documents_patients_patients_path %>"
       class="p-3 rounded-xl transition-colors <%= selected == 'documents' ? 'text-sky-600 hover:text-slate-700 bg-sky-100 hover:bg-sky-100/90' : 'hover:bg-slate-200/60 text-slate-400 hover:text-slate-500' %>"
       title="Documents">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-5 w-5">
        <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
        <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
        <path d="M10 9H8"></path>
        <path d="M16 13H8"></path>
        <path d="M16 17H8"></path>
      </svg>
      <span class="sr-only">Documents</span>
    </a>

    <a href="#"
       class="p-3 rounded-xl transition-colors <%= selected == 'billing' ? 'text-sky-600 hover:text-slate-700 bg-sky-100 hover:bg-sky-100/90' : 'hover:bg-slate-200/60 text-slate-400 hover:text-slate-500' %>"
       title="Billing">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pound-sterling h-5 w-5">
        <path d="M18 7c0-5.333-8-5.333-8 0"></path>
        <path d="M10 7v14"></path>
        <path d="M6 21h12"></path>
        <path d="M6 13h10"></path>
      </svg>
      <span class="sr-only">Billing</span>
    </a>
  </div>
</div>
