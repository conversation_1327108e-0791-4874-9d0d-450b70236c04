# frozen_string_literal: true

module Patients
  class MedicalHistoriesController < Patients::ApplicationController
    # layout 'medical_history'
    before_action :set_current_practice

    def new
      @questions = MedicalHistoryQuestion.where(medical_history_answer_id: nil,
                                                practice_id: [nil, current_patient.current_practice_id]).order(:position)
      @medical_history = MedicalHistory.new(
        patient_id: current_patient.id,
        created_by_id: current_patient.id
      )
    end

    def create
      @medical_history = MedicalHistory.new(
        patient: current_patient,
        created_by: User.first
      )

      if @medical_history.save
        parse_responses(params[:responses] || {})
        flash[:success] = 'Medical history updated successfully'
        redirect_to submitted_patients_medical_histories_path
      else
        @questions = MedicalHistoryQuestion.where(medical_history_answer_id: nil)
        render :new, status: :unprocessable_entity
      end
    end

    def show
      @medical_history = MedicalHistory.find_by(key: params[:id])
      @questions = MedicalHistoryQuestion.where(medical_history_answer_id: nil)
    end

    def submitted; end

    private

    def parse_responses(responses_hash)
      responses_hash.each do |question_id, data|
        question = MedicalHistoryQuestion.find(question_id)
        parse_question_responses(question, data)
      end
    end

    def parse_question_responses(question, data)
      case question.question_type
      when 'multiple_answers', 'single_answer'
        selected_answer_ids = Array(data['answers'])
        selected_answer_ids.each do |answer_id|
          @medical_history.medical_histories_medical_history_answers.create!(
            medical_history_answer_id: answer_id
          )
        end
      when 'text'
        (data['answers'] || {}).each do |answer_id, typed_value|
          @medical_history.medical_histories_medical_history_answers.create!(
            medical_history_answer_id: answer_id,
            value: typed_value
          )
        end
      when 'medication_select'
        selected_meds = Array(data['answers'])
        answer = question.medical_history_answers.first
        @medical_history.medical_histories_medical_history_answers.create!(
          medical_history_answer_id: answer.id,
          value: selected_meds.join(',')
        )
      end

      if data['followups'].present?
        data['followups'].each do |sub_q_id, sub_data|
          sub_question = MedicalHistoryQuestion.find(sub_q_id)
          parse_question_responses(sub_question, sub_data)
        end
      end
    end

    def set_current_practice
      @practice = current_patient.current_practice || current_patient.practices.first
    end
  end
end
